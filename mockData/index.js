/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-04-26 15:37:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-05 10:45:20
 * @FilePath: /zcy-announcement-v2-front/mockData/index.js
 * @Description:
 */
const Mock = require("mockjs");

module.exports = {
  "/announcement/api/small/exportAnnouncementInfo": function () {
    return {
      success: true,
      result: {
        taskUuid: "1234567890",
      },
    };
  },
  "/announcement/api/small/pollingAnnouncementInfo": function () {
    return {
      success: true,
      result: {
        exportFileUrl: "http://www.baidu.com",
      },
    };
  },
  "/announcement/api/small/showExportButton": function () {
    return {
      success: true,
      result: true,
    };
  },
  "/announcement/api/small/listSelfAnnouncement": function () {
    return {
      success: true,
      result: {
        data: [],
      },
    };
  },
  "/user/privileges/isResourcePermit": function () {
    return {
      success: true,
      result: {
        isPermit: true,
        accessType: 2,
        error: "user not authorization",
        errorType: 403,
      },
      error: null,
    };
  },
  "/api/test": function () {
    const data = Mock.mock({
      "list|1-10": [
        {
          name: Mock.Random.cname(),
          "age|18-60": 1,
          email: Mock.mock("@EMAIL()"),
        },
      ],
    });
    return data.list;
  },
  "/announcement/api/config/form/listYear": function () {
    return {
      success: true,
      result: [2023, 2022, 2021, 2020],
      error: null,
    };
  },
  "/api/biz-bidding/doctor-manhattan/project-manage/sync-metadata":
    function () {
      return true;
    },
  "/announcement/sensitive/detailAnnouncementSensitive": function () {
    return {
      success: true,
      result: {
        attachmentResultList: [
          {
            fileName: "111.pdf",
            fileUrl: "http://www.baidu.com",
            fileSensitiveStatus: 3,
            sensitiveWords: "",
          },
          {
            fileName: "2222.pdf",
            fileUrl: "http://www.baidu.com",
            fileSensitiveStatus: 2,
            sensitiveWords: "打人，大炮",
          },
        ],
        id: -41963246,
        announcementId: -90744011,
        announcementTitle: "voluptate sed ullamco in est",
        statusName: "通过",
        sensitiveWords: "杀人,放火",
        announcementStatusName: "consectetur enim eiusmod cillum",
        announcementStatusCode: "do culpa",
        announcementSerialNum: "eu",
        announcementReleasedAt: "1982-06-08 12:51:43",
        annBigType: 29323697,
        exitSensitiveTitleAndContent: true,
      },
      error: "qui dolore reprehenderit",
    };
  },
  "/announcement/api/nonGovernmentProcurement": function () {
    return {
      success: true,
      result: {
        needPopUpWindow: true,
        whetherToSettleIn: false,
      },
    };
  },
  "/announcement/api/small/barButtons": function () {
    return {
      success: true,
      result: {
        canCreate: true,
        canGenerateAnnualData: true,
        canDisplayAnnualSearch: true,
        canDisplayOrgNameSearch: true,
        isResponsibleBudgetUnit: true,
      },
    };
  },
//   // 书签列表
//   "/api/opPlatform/prodOperation/announcement/bookmark/list": function () {
//     return {
//       success: false,
//       result: {
//         pageNum: 1,
//         pageSize: 20,
//         pages: 10,
//         total: 200,
//         data: [
//           {
//             id: -71402666,
//             code: "projectOverview",
//             name: "项目概况",
//             type: "输入框",
//             stdlibId: 61169403,
//             stdlibCode: "eu",
//             stdlibName: "dolor eu officia laboris",
//             status: 1,
//             createName: "捷捷",
//             createTime: "2011-10-20 17:59:06",
//             modifyName: "quis Duis et velit",
//             modifyTime: "1983-09-15 15:42:35",
//             children: [
//               {
//                 id: 43730412,
//                 code: "bidTime",
//                 name: "投标日期",
//                 type: "日期时间",
//                 stdlibId: -10260636,
//                 stdlibCode: "dolor deserunt Lorem quis voluptate",
//                 stdlibName: "sunt culpa enim",
//                 status: 1,
//                 createName: "哈哈",
//                 createTime: "1974-06-27 21:37:08",
//                 modifyName: "consectetur velit do",
//                 modifyTime: "1971-12-19 03:37:38",
//                 buttonList: [
//                   {
//                     code: "edit",
//                     name: "编辑",
//                   },
//                   {
//                     code: "detail",
//                     name: "详情",
//                   },
//                   {
//                     code: "addChildren",
//                     name: "添加子字段",
//                   },
//                 ],
//               },
//             ],
//             buttonList: [
//               {
//                 code: "detail",
//                 name: "详情",
//               },
//               {
//                 code: "delete",
//                 name: "删除",
//               },
//             ],
//           },
//           {
//             id: 43730412,
//             code: "bidTime",
//             name: "投标日期",
//             type: "日期时间",
//             stdlibId: -10260636,
//             stdlibCode: "dolor deserunt Lorem quis voluptate",
//             stdlibName: "sunt culpa enim",
//             status: 0,
//             createName: "哈哈",
//             createTime: "1974-06-27 21:37:08",
//             modifyName: "consectetur velit do",
//             modifyTime: "1971-12-19 03:37:38",
//             buttonList: [
//               {
//                 code: "disable",
//                 name: "停用",
//               },
//               {
//                 code: "detail",
//                 name: "详情",
//               },
//               {
//                 code: "template",
//                 name: "模板引用情况",
//               },
//             ],
//           },
//           {
//             id: -35000569,
//             code: "sunt culpa",
//             name: "sed nulla",
//             type: "eu laboris esse",
//             stdlibId: 2425607,
//             stdlibCode: "Ut aute",
//             stdlibName: "ad culpa cupidatat voluptate sed",
//             status: 1,
//             createName: "enim occaecat quis dolor",
//             createTime: "1995-05-20 16:28:31",
//             modifyName: "consectetur et",
//             modifyTime: "2010-10-07 04:31:11",
//             buttonList: [
//               {
//                 code: "enable",
//                 name: "启用",
//               },
//               {
//                 code: "disable",
//                 name: "停用",
//               },
//             ],
//           },
//         ],
//       },
//       code: "dolor consectetur",
//       message: "minim id",
//     };
//   },
//   "/api/opPlatform/prodOperation/announcement/bookmark/save": function () {
//     return {
//       success: true,
//       result: true,
//       message: "success",
//     };
//   },
//   // 字典
//   "/api/opPlatform/prodOperation/announcement/dict": function () {
//     return {
//       success: true,
//       result: {
//         componentType: [
//           {
//             value: 1,
//             label: "下拉列表",
//           },
//           {
//             value: 2,
//             label: "日期选择控件",
//           },
//           {
//             value: 5,
//             label: "数值输入框",
//           },
//           {
//             value: 6,
//             label: "单行文本输入框",
//           },
//           {
//             value: 7,
//             label: "多行文本输入框",
//           },
//           {
//             value: 10,
//             label: "Table表格面板输入",
//           },
//           {
//             value: 11,
//             label: "间隔文本框",
//           },
//           {
//             value: 13,
//             label: "时间选择控件",
//           },
//           {
//             value: 14,
//             label: "日期时间选择控件",
//           },
//           {
//             value: 25,
//             label: "下拉单选列表（支持自定义）",
//           },
//           {
//             value: 999,
//             label: "自定义控件",
//           },
//         ],
//         bizType: [
//           {
//             value: 1,
//             label: "政采",
//           },
//         ],
//         stdlibTag: [
//           {
//             value: "announcementContent",
//             label: "正文渲染",
//           },
//           {
//             value: "manualAnnouncementForm",
//             label: "表单渲染",
//           },
//           {
//             value: "push",
//             label: "推送",
//           },
//           {
//             value: "customize",
//             label: "自定义",
//           },
//         ],
//         stdlibStatus: [
//           {
//             value: 1,
//             label: "启用",
//           },
//           {
//             value: 0,
//             label: "停用",
//           },
//         ],
//       },
//       code: null,
//       message: null,
//       error: null,
//     };
//   },
//   // 请求书签
//   "/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark":
//     function () {
//       return {
//         success: true,
//         result: {
//           pageNo: 1,
//           pageSize: 10,
//           pages: 1,
//           total: 20,
//           data: [
//             {
//               code: "aliquip",
//               name: "eu",
//               type: "输入框",
//             },
//             {
//               code: "dolor",
//               name: "aliqua sint et",
//               type: "输入框",
//             },
//             {
//               code: "minim laboris",
//               name: "ea",
//               type: "输入框",
//             },
//           ],
//         },
//         code: "mollit laborum",
//         message: "dolore in laborum nostrud eiusmod",
//       };
//     },

//   // 显示使用书签和模板
//   "/api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo":
//     function () {
//       return {
//         success: true,
//         result: [
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 111111,
//                 templateCode: "222222",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//               {
//                 templateId: 111111,
//                 templateCode: "222222",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "hhhhhh",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//         ],
//       };
//     },
//   // 书签详情
//   "/api/opPlatform/prodOperation/announcement/bookmark/detail": function () {
//     return {
//       success: true,
//       result: {
//         id: 71402666,
//         code: "projectOverview",
//         name: "项目概况",
//         type: "输入框",
//         stdlibId: 61169403,
//         stdlibCode: "eu",
//         stdlibName: "dolor eu officia laboris",
//         status: "ut",
//         createName: "捷捷",
//         bizType:1,
//         createTime: "2011-10-20 17:59:06",
//         modifyName: "quis Duis et velit",
//         modifyTime: "1983-09-15 15:42:35",
//         children: [
//           {
//             id: 111111402666,
//             code: "projectOverview",
//             name: "项目概况1",
//             type: "输入框",
//             stdlibId: 61169403,
//             stdlibCode: "eu",
//             stdlibName: "dolor eu officia laboris",
//             status: "ut",
//             createName: "捷捷",
//             createTime: "2011-10-20 17:59:06",
//             modifyName: "quis Duis et velit",
//             modifyTime: "1983-09-15 15:42:35",
//           },
//         ],
//       },
//     };
//   },
//   // 启用书签
//   "/api/opPlatform/prodOperation/announcement/bookmark/enable": function () {
//     return {
//       success: true,
//       result: true,
//     };
//   },
//   // 停用书签
//   "/api/opPlatform/prodOperation/announcement/bookmark/disable": function () {
//     return {
//       success: true,
//       result: true,
//     };
//   },
//   // 标准字段列表
//   "/api/opPlatform/prodOperation/announcement/stdlib/list": function () {
//     return {
//       success: true,
//       result: {
//         pageNo: 1,
//         pageSize: 20,
//         pages: 10,
//         total: 200,
//         data: [
//           {
//             children: [
//               {
//                 children: [],
//                 buttonList: [
//                   {
//                     code: "edit",
//                     name: "编辑",
//                   },
//                   {
//                     code: "disable",
//                     name: "停用",
//                   },
//                   {
//                     code: "delete",
//                     name: "删除",
//                   },
//                   {
//                     code: "detail",
//                     name: "详情",
//                   },
//                   {
//                     code: "template",
//                     name: "模板引用情况",
//                   },
//                 ],
//                 id: 82537585,
//                 parentId: -70674940,
//                 code: "ad in proident anim consequat",
//                 name: "项目名称",
//                 bizType: 1,
//                 componentType: 1,
//                 ifRow: 1,
//                 ifMust: 1,
//                 // minCNLength: -78695068,
//                 multiType: 2,
//                 remark: "labore eiusmod",
//                 example: "amet voluptate anim",
//                 status: 0,
//                 creatorName: "eu enim ad dolor",
//                 createTime: "1997-02-24 04:47:47",
//                 modifierName: "Ut in culpa",
//                 modifyTime: "2007-12-06 15:58:25",
//                 tags: ["manualAnnouncementForm", "push"],
//               },
//             ],
//             buttonList: [
//               {
//                 code: "edit",
//                 name: "编辑",
//               },
//               {
//                 code: "detail",
//                 name: "详情",
//               },
//               {
//                 code: "addChildren",
//                 name: "添加子字段",
//               },
//             ],
//             id: 1106749,
//             parentId: 46406030,
//             code: "eiusmod",
//             name: "项目概况",
//             bizType: 1,
//             componentType: 2,
//             ifRow: 0,
//             ifMust: 0,
//             minCNLength: 40620272,
//             multiType: 50664962,
//             remark: "in",
//             example: "aliquip",
//             status: 1,
//             creatorName: "est sed Duis",
//             createTime: "1979-08-01 21:24:59",
//             modifierName: "voluptate",
//             modifyTime: "2005-10-20 13:29:08",
//             tags: ["manualAnnouncementForm", "push"],
//           },
//         ],
//       },
//     };
//   },

//   // 保存标准字段
//   "/api/opPlatform/prodOperation/announcement/stdlib/save": function () {
//     return {
//       "success": true,
//       "result": true,
//       "code": "aliqua dolore officia",
//       "message": "Duis qui"
//     }
//   },

//   // 标准字段详情
//   "/api/opPlatform/prodOperation/announcement/stdlib/detail": function () {
//     return {
//       "success": true,
//       "result": {
//         "children": [
//           {
//             "id": -99826795,
//             "parentId": -27170506,
//             "code": "cupidatat Lorem",
//             "name": "amet est",
//             "bizType": 1,
//             "componentType": 1,
//             "ifRow": 1,
//             "ifMust": 1,
//             // "minCNLength": 7752490,
//             "multiType": 52775165,
//             "remark": "pariatur magna aliquip exercitation",
//             "example": "ullamco in",
//             "status": 1,
//             "creatorName": "eiusmod in cillum",
//             "createTime": "2023-10-15 10:29:04",
//             "modifierName": "labore reprehenderit nulla",
//             "modifyTime": "2010-01-27 19:38:17"
//           }
//         ],
//         "tags": ["announcementContent","manualAnnouncementForm"],
//         "id": -7884450,
//         "parentId": -93376692,
//         "code": "ut ut",
//         "name": "ea laboris elit",
//         "bizType": 1,
//         "componentType": 2,
//         "ifRow": 1,
//         "ifMust": 1,
//         // "minCNLength": 26493700,
//         "multiType": 54160379,
//         "remark": "esse ullamco quis exercitation nisi",
//         "example": "commodo in",
//         "status": 0,
//         "creatorName": "anim adipisicing nostrud voluptate commodo",
//         "createTime": "1990-10-25 17:46:58",
//         "modifierName": "Excepteur est dolore laborum non",
//         "modifyTime": "2019-02-16 04:53:39"
//       },
//       "code": "proident id",
//       "message": "ex Excepteur in exercitation Ut"
//     }
//   },

//   // 启用标准字段
//   "/api/opPlatform/prodOperation/announcement/stdlib/enable": function () {
//     return {
//       "success": true,
//       "result": true,
//       "code": "esse id anim",
//       "message": "veniam consequat ex cillum ipsum"
//     }
//   },

//   // 停用标准字段
//   "/api/opPlatform/prodOperation/announcement/stdlib/disable": function () {
//     return {
//       "success": true,
//       "result": true,
//       "code": "esse id anim",
//       "message": "veniam consequat ex cillum ipsum"
//     }
//   },

//   // 删除标准字段
//   "/api/opPlatform/prodOperation/announcement/stdlib/delete": function () {
//     return {
//       "success": true,
//       "result": true,
//       "code": "esse id anim",
//       "message": "veniam consequat ex cillum ipsum"
//     }
//   },

//   // 显示使用书签和模板
//     "/api/opPlatform/prodOperation/announcement/stdlib/showUsedInfo":
//     function () {
//       return {
//         success: true,
//         result: [
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 111111,
//                 templateCode: "222222",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//               {
//                 templateId: 111111,
//                 templateCode: "222222",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "hhhhhh",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//           {
//             bookmark: "项目名称",
//             templates: [
//               {
//                 templateId: 28868542,
//                 templateCode: "quis esse",
//                 defaultTemplateFileId: 54600497,
//                 templateName: "do velit exercitation",
//                 templateLevelName: "Lorem ut ex aliqua",
//                 templateFileVersion: "laborum cupidatat minim id",
//               },
//             ],
//           },
//         ],
//         "code": "mollit laboris commodo ad incididunt",
//         "message": "exercitation enim adipisicing dolore laboris"
//       };
//     },
//   "/api/opPlatform/prodOperation/announcement/referConfig/list": function () {
//     return {
//       "success": true,
//       "result": [
//           {
//               "id": -6460937,
//               "order": -31826539,
//               "stdlibCode": "dolore ut aliqua anim",
//               "stdlibName": "consequat",
//               "stdlibType": 1,
//               "forFormRender": false,
//               "forAnnPush": true,
//               "referStatus": 1,
//               "modifyName": "ut eu aliqua pariatur",
//               "modifyTime": "Lorem",
//               "buttonList": [
//                   {
//                       "code": "edit",
//                       "name": "编辑"
//                   },
//                   {
//                       "code": "detail",
//                       "name": "详情"
//                   },
//                   {
//                       "code": "delete",
//                       "name": "删除"
//                   }
//               ],
//               "children": [
//                   {
//                       "id": 45023780,
//                       "order": 84700917,
//                       "stdlibCode": "Duis",
//                       "stdlibName": "culpa ut exercitation",
//                       "stdlibType": 2,
//                       "forFormRender": false,
//                       "forAnnPush": true,
//                       "referStatus": 1,
//                       "modifyName": "dolore ipsum reprehenderit",
//                       "modifyTime": "Duis officia in",
//                       "buttonList": [
//                           {
//                               "code": "edit",
//                               "name": "编辑"
//                           },
//                           {
//                               "code": "detail",
//                               "name": "详情"
//                           },
//                           {
//                               "code": "delete",
//                               "name": "删除"
//                           },
//                           {
//                               "code": "addChildren",
//                               "name": "添加子字段"
//                           },
//                       ],
//                       "children": []
//                   },
//                   {
//                       "id": 28110832,
//                       "order": -10411346,
//                       "stdlibCode": "commodo",
//                       "stdlibName": "sed ut ullamco deserunt",
//                       "stdlibType": 6,
//                       "forFormRender": true,
//                       "forAnnPush": true,
//                       "referStatus": 0,
//                       "modifyName": "nulla mollit velit",
//                       "modifyTime": "nisi laborum",
//                       "buttonList": [
//                           {
//                               "code": "Excepteur",
//                               "name": "nulla velit ut dolor Ut"
//                           }
//                       ],
//                       "children": []
//                   }
//               ]
//           }
//       ],
//       "code": "amet",
//       "message": "cillum ea non esse id"
//   };
//   },
//   "/api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib": function () {
//     return {
//       "success": true,
//       "result": {
//           "pageNo": -81815674,
//           "pageSize": 81414917,
//           "pages": -30782732,
//           "total": -18772654,
//           "data": [
//               {
//                   "stdlibCode": "ad incididunt ullamco eiusmod labore",
//                   "stdlibName": "esse"
//               },
//               {
//                   "stdlibCode": "consequat",
//                   "stdlibName": "pariatur sed est"
//               },
//               {
//                   "stdlibCode": "officia dolor elit sint",
//                   "stdlibName": "elit tempor"
//               }
//           ]
//       },
//       "code": "voluptate sint dolore",
//       "message": "Lorem culpa dolore labore ad"
//   };
//   },

//   // 删除书签
//   "/api/opPlatform/prodOperation/announcement/bookmark/delete": function () {
//     return {
//       "success": true,
//       "result": true,
//       "code": "esse id anim",
//       "message": "veniam consequat ex cillum ipsum"
//     }
//   },

//     "/api/opPlatform/prodOperation/announcement/logs": function () {
//       return {
//         "success": false,
//         "result": {
//           "pageNo": 1,
//           "pageSize": 10,
//           "pages": 63458975,
//           "total": 12,
//           "data": [
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },            {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             },
//             {
//               "id": 18911399,
//               "bizId": -46975552,
//               "districtCode": "ipsum ut elit eiusmod officia",
//               "announcementType": -73380004,
//               "announcementTypeName": "dolor cupidatat amet Excepteur sit",
//               "module": "irure mollit Ut esse ullamco",
//               "submodule": "sed pariatur do officia",
//               "submoduleName": "officia",
//               "operationType": "ex occaecat qui",
//               "operationTypeName": "officia adipisicing consectetur ullamco",
//               "message": "consectetur dolore",
//               "operatorId": 67470136,
//               "operatorName": "sit veniam in pariatur",
//               "addTime": "1976-08-28 20:35:46",
//               "modifiedTime": "2000-10-11 03:15:59"
//             }
//           ]
//         },
//         "code": "laborum Excepteur magna nostrud",
//         "message": "cupidatat ex do"
//       }
//     },

//     "/api/opPlatform/prodOperation/announcement/queryAllEnv": function () {
//       return {
//         "success": true,
//         "result": [
//         {
//         "name": "测试",
//         "code": "test",
//         "domain": "test",
//         "freeway": "minim laboris deserunt"
//         },
//         {
//         "name": "预发",
//         "code": "staging",
//         "domain": "staging",
//         "freeway": "sunt culpa esse"
//         },
//         {
//         "name": "consectetur",
//         "code": "aliqua dolore aliquip dolor",
//         "domain": "consequat ut exercitation",
//         "freeway": "sint aute eu et"
//         },
//         ],
//         "code": "dolor sit veniam",
//         "message": "aute velit aliqua cupidatat"
//         }        
//     }
};
