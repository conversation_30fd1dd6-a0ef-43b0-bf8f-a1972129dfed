{"name": "zcy-announcement-v2-front", "author": "小歪", "version": "1.0.0", "description": "公告v2react版", "main": "src/main.js", "keywords": ["zcy", "react"], "repository": {"type": "git", "url": "*******************:paas-front/zcy-announcement-v2-front.git"}, "scripts": {"api": "npx ytt", "dev:api": "npm run api && zoo dev", "zicon": "zicon", "build": "zoo build", "dev": "zoo dev", "testbuild": "zoo build --env test", "lint-staged": "lint-staged", "lint-staged:es": "eslint", "precommit": "lint-staged", "lint": "npm run lint:es && npm run lint:style", "lint:es": "eslint src --ext '.js,.jsx'", "lint:style": "stylelint \"src/**/*.less\" --syntax less", "fix": "eslint --fix src --ext '.js,.jsx'"}, "lint-staged": {"src/**/*.{js,jsx}": ["npm run lint-staged:es"], "src/**/*.less": ["stylelint --config  ./.stylelintrc --fix", "git add"], "*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "git add"]}, "license": "MIT", "dependencies": {"@antv/g2": "3.1.1", "@zcy/back-sky-sdk": "^1.0.0", "@zcy/basic-sdk": "2.1.2", "@zcy/ca-sdk": "1.2.61", "@zcy/doraemon": "4.2.5-beta.0", "@zcy/form-page": "6.0.8-beta.3", "@zcy/pc-xmcg-flow-utils-front": "2.0.0-beta.5", "@zcy/react-hooks": "^2.0.0", "@zcy/utils": "^1.0.12", "@zcy/workflow-steps": "1.0.0", "@zcy/zcy-audit-flow-back": "2.0.4", "@zcy/zcy-ca-operate-modal-back": "2.0.0", "@zcy/zcy-ca-qr-code-back": "2.0.0", "@zcy/zcy-list-component-back": "2.0.0", "@zcy/zcy-project-layout-back": "5.0.4-beta.2", "@zcy/zcy-purchase-manage-lib-back": "2.0.0-beta.1", "@zcy/zcy-ueditor-front": "1.0.8", "ahooks": "^3.8.5", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "console-polyfill": "^0.3.0", "dva": "^2.2.2", "dva-loading": "^2.0.5", "file-saver": "^2.0.0-rc.3", "jquery": "^3.5.1", "jsdom": "^16.4.0", "location": "0.0.1", "moment": "^2.29.2", "navigator": "^1.0.1", "raf": "^3.4.0", "react": "^16.6.1", "react-countup": "^3.0.3", "react-dom": "^16.6.1", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.20.13", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@zcy/babel-plugin-compatible-object-destructuring": "^1.0.2", "@zcy/commitlint-config-zoo": "0.0.1", "@zcy/eslint-config-zoo": "^1.0.15", "@zcy/prettier-config-zoo": "0.0.1-beta", "@zcy/stylelint-config-zoo": "0.0.4", "@zcy/zoo-cli-plugin-react": "^1.0.0", "babel-core": "^6.26.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.2.5", "babel-plugin-dva-hmr": "^0.4.1", "babel-plugin-import": "^1.13.6", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "cross-env": "^7.0.2", "eslint-config-airbnb": "^16.1.0", "eslint-config-zoo": "^1.0.4", "eslint-plugin-babel": "^4.1.2", "eslint-plugin-compat": "^2.5.1", "eslint-plugin-jsx-a11y": "^6.0.3", "event-source-polyfill": "^1.0.15", "husky": "^0.14.3", "lint-staged": "^7.2.2", "mockjs": "^1.0.1-beta3", "qs": "^6.9.4", "redbox-react": "^1.5.0", "stylelint-config-standard": "^37.0.0", "typescript": "^4.9.5", "yapi-to-typescript": "^3.37.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 5.0.0"}, "browserslist": ["> 1%", "last 10 versions", "not ie <= 8"], "projectTemplateVersion": "1.0.1", "buildScriptVersion": "2.0.0", "metadata": {"script": "leo-middle-react-pc-project", "project": "application", "platform": "pc", "framework": "React"}, "migrateTs": true, "verify": true, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}