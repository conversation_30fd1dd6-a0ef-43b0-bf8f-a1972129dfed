import React, { useEffect, useState } from 'react';
import { parseEnvType } from '@/utils/utils';
import Cookies from 'js-cookie';
import _find from 'lodash/find';
import { Icon, Menu, Dropdown, message, Modal, request } from 'doraemon';
import './index.less';

interface EnvItemType {
    code: string
    name: string
}
type EnvListType = EnvItemType[]
interface EnvSwitchProps {
  disabled?: boolean;
  onReady?: () => void; // 添加 onReady 回调参数
}
const checkEnvTimer = 2000;
const refreshDelayTime = 2000;
export default ({ disabled = false, onReady }: EnvSwitchProps) => {
  const [envList, setEnvList] = useState<EnvListType>([]);

  const [currentEnv, setCurrentEnv] = useState<EnvItemType | null>(null);

  useEffect(() => {
    request('/api/opPlatform/prodOperation/announcement/queryAllEnv')
      .then((res) => {
        if (res && res.success) {
          const envListRes = res.result || [];
          setEnvList(envListRes);
          const cookieEnv = Cookies.get('envName');
          if (cookieEnv) {
            const envInfo = _find(envListRes, env => env.code === cookieEnv) 
            || envListRes?.[0] || null;
            if (envInfo) {
              Cookies.set('envName', envInfo.code);
              setCurrentEnv(envInfo);
            }
            onReady?.();
            return;
          }
          const defaultEnv = res.result?.[0] || [];
          if (defaultEnv?.code) {
            Cookies.set('envName', defaultEnv.code);
            setCurrentEnv(defaultEnv);
            onReady?.();
          }
        } else {
          message.error(res.message);
        }
      });
  }, []);
  // 监测环境是否有变更
  const checkEnvInfo = () => {
    const cookieEnv = Cookies.get('envName');
    if (currentEnv?.code && cookieEnv && !document.hidden) {
      if (currentEnv.code === cookieEnv) return;
      const modal = Modal.warning({
        title: '监听到当前环境已切换',
        content: `将在${refreshDelayTime / 1000}秒后刷新当前页面`,
      });
      const timer = setTimeout(() => {
        modal.destroy();
        window.location.reload();
        clearTimeout(timer);
      }, refreshDelayTime);
    }
  };

  useEffect(() => {
    // 检查环境是否有变更 / 2s
    const checkInterval = setInterval(() => {
      checkEnvInfo();
    }, checkEnvTimer);

    return () => {
      checkInterval && clearInterval(checkInterval);
    };
  }, [currentEnv]);

  const handleEnvChange = (env: EnvItemType) => {
    Modal.confirm({
      title: '切换环境',
      content: '切换后，将跳转到当前环境的列表页',
      okText: '切换',
      onOk: () => {
        Cookies.set('envName', env.code);
        setCurrentEnv(env);
        window.location.reload();
      },
    });
  };
  return envList.length ? (
    <Dropdown
      disabled={disabled}
      overlay={(
        <Menu>
          {
            envList.map((env) => {
              return (
                <Menu.Item key={env.code}>
                  <a onClick={() => handleEnvChange(env)}>{env.name}</a>
                </Menu.Item>
              );
            })
          }
        </Menu>
      )}
      getPopupContainer={triggerNode => triggerNode.parentNode}
    >
      <div 
        className={`env-switch-title ${disabled ? 'disabled' : ''} 
env-switch-level-${parseEnvType(currentEnv?.code)}`}
      >
        当前环境：
        {currentEnv?.name} <Icon style={{ marginRight: 8, fontSize: 12 }} type="caret-down" />
      </div>
    </Dropdown>
  ) : null;
};
