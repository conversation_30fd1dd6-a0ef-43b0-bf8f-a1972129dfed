import React, { useEffect } from 'react';
import PaaSLayout from '@/layouts/PaaSLayout';
import { Spin, Form, FormGrid, Panel } from 'doraemon';
import { standardFieldUseDetailConfig } from '../../config/standardFieldUseDetailConfig';
import { handleFormGrid } from '../../components/handleFormFields';
import useStandardFieldUseDetailApi from './hooks/useStandardFieldUseDetailApi';
import { transformedData, getLastPathParam } from '../../utils';

const featureFormItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 15 },
  column: 2,
  bordered: true,
};

export default function StandardFieldUseDetail() {
  const {
    discList,
    fetchDictList,
    fetchDetail,
    detailData,
    detailLoading,
    logList,
    logTotal,
    fetchLog,
  } = useStandardFieldUseDetailApi();

  const detailId = getLastPathParam();

  useEffect(() => {
    fetchDetail({ id: detailId });
    fetchLog({
      bizId: detailId,
      module: 'AnnouncementReferConfig',
      pageNo: 1,
      pageSize: 10,
    });
    fetchDictList({
      codes: ['bizType', 'stdlibStatus', 'componentType', 'stdlibTag'],
    });
  }, []);

  // 日志信息列表切换分页
  const handleLogTableChange = (pageNo) => {
    fetchLog({
      bizId: detailId,
      module: 'AnnouncementReferConfig',
      pageNo,
      pageSize: 10,
    });
  };

  const getFormItem = () => {
    const fields = standardFieldUseDetailConfig('detail');
    const record = transformedData(detailData, discList);
    return handleFormGrid({
      fields,
      record,
      discList,
      logInfoList: logList,
      logInfoTotal: logTotal,
      handleLogTableChange,
    });
  };

  const breadcrumb = [
    {
      label: '标准字段使用配置列表',
    },
    {
      label: '详情',
    },
  ];

  return (
    <PaaSLayout
      routes={breadcrumb}
      envDisabled
      globalBtn={[
        {
          label: '返回',
          onClick: () => {
            history.back();
          },
        },
      ]}
    >
      <Spin spinning={detailLoading}>
        <Panel>
          <Form>
            <FormGrid
              {...featureFormItemLayout}
              formGridItem={getFormItem() || []}
            />
          </Form>
        </Panel>
      </Spin>
    </PaaSLayout>
  );
}
