/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 16:40:18
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 11:19:59
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useRequest } from '@zcy/react-hooks';
import {
  fetchBookmarkList,
  fetchTemplateUseList,
  fetchEnableBookmark,
  fetchDisableBookmark,
  fetchDictList,
  fetchDeleteBookmark,
  fetchButtonList,
} from '../../../services';

export default function useBookMarkApi() {
  // 获取书签列表
  const bookmarkListRes = useRequest(fetchBookmarkList, {
    manual: true,
  });
  const bookmarkListResult = bookmarkListRes?.data?.result || [];

  // 显示使用书签和模板
  const templateUseListRes = useRequest(
    (params) => {
      return fetchTemplateUseList(params);
    },
    {
      manual: true,
    }
  ) || [];
  const templateUseListResult = templateUseListRes?.data?.result?.flatMap(item => 
    item.templates.map(template => ({
      bookmark: item.bookmark,
      templateCode: template.templateCode,
      templateName: template.templateName,
      templateId: template.templateId,
    }))
  );

  // 启用书签
  const enableBookmarkRes = useRequest(
    (params) => {
      return fetchEnableBookmark(params);
    },
    {
      manual: true,
    }
  );

  // 停用书签
  const disableBookmarkRes = useRequest(
    (params) => {
      return fetchDisableBookmark(params);
    },
    {
      manual: true,
    }
  );

  // 删除书签
  const deleteBookmarkRes = useRequest(
    (params) => {
      return fetchDeleteBookmark(params);
    },
    {
      manual: true,
    }
  );

  // 获取数据字典
  const discListRes = useRequest(
    (params) => {
      return fetchDictList(params);
    },
    {
      manual: true,
    }
  );
  const discListResult = discListRes?.data?.result;


  // 获取主按钮
  const buttonListRes = useRequest(
    (params) => {
      return fetchButtonList(params);
    },
    {
      manual: true,
    }
  );
  const mainButtonList = buttonListRes?.data?.result?.buttonList;

  return {
    discList: discListResult,
    fetchDictList: discListRes.runAsync,
    bookmarkList: bookmarkListResult?.data || [],
    bookmarkListPagination: {
      // showSizeChanger: true,
      total: bookmarkListResult?.total,
    },
    bookmarkListLoading: bookmarkListRes.loading,
    fetchBookmarkList: bookmarkListRes.runAsync,
    templateUseList: templateUseListResult || [],
    fetchTemplateUseList: templateUseListRes.runAsync,
    templateInfoLoading: templateUseListRes.loading,
    fetchEnableBookmark: enableBookmarkRes.runAsync,
    fetchDisableBookmark: disableBookmarkRes.runAsync,
    fetchDeleteBookmark: deleteBookmarkRes.runAsync,
    mainButtonList,
    fetchButtonList: buttonListRes.runAsync,
  };
}
