/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-20 18:01:30
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 15:48:54
 * @FilePath: /zcy-announcement-v2-front/src/routes/StanaedFieldManage/views/list.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useCallback } from 'react';
import PaaSLayout from '@/layouts/PaaSLayout';
import { ColumnProps } from '@zcy/doraemon/lib/table/interface';
import { Spin, ZcyList, Input, Select, Table, message } from '@zcy/doraemon';
import useBookMarkApi from './hooks/useBookMarkApi';
import useBookmarkState from './hooks/useBookMarkState';
import AddOrEditBookmark from '../../components/AddOrEditBookmark';
import SelectBookMarkModal from '../../components/SelectBookMarkModal';

const { Actions } = Table;
export default function BookMarkList() {
  const BUTTON_CONFIG = {
    // 新增标准字段
    addBookmark: {
      type: 'primary',
      onClick: () => addBookMark(),
    },
  };
  const {
    discList, // 静态字典
    fetchDictList,
    bookmarkList, // 书签列表时数据
    fetchBookmarkList, //
    bookmarkListLoading,
    bookmarkListPagination,
    templateUseList,
    fetchTemplateUseList,
    templateInfoLoading,
    fetchEnableBookmark,
    fetchDisableBookmark,
    fetchDeleteBookmark,
    mainButtonList,
    fetchButtonList,
  } = useBookMarkApi();
  const {
    bizType: bizTypeList = [], // 业务类型
    stdlibStatus: stdlibStatusList = [], // 状态
  } = discList;
  const {
    addOrEditBookmarkVisible,
    changeAddOrEditBookmarkVisible,
    selectBookMarkVisible,
    changeSelectBookMarkVisible,
    selectedData,
    getSelectedData,
    mode,
    changeMode,
    // parentId,
    changeParentId,
    newSearchParams,
    setSearchParams,
  } = useBookmarkState();

  const bookmarkInitSearchParams = {
    pageNo: 1,
    pageSize: 20,
    bizType: 1, // 政采业务
  };

  const handleButton = useCallback(() => {
    return mainButtonList?.map(btn => {
      const config = BUTTON_CONFIG[btn.code];
      return config ? {
        ...config,
        label: btn?.name,
      } : null;
    }).filter(Boolean);
  }, [mainButtonList]);

  useEffect(() => {
    // 数据字典
    fetchDictList({ codes: ['bizType', 'stdlibStatus'] });
    // 获取全局按钮列表
    fetchButtonList({ module: 'bookmark' });
  }, []);

  // 搜索
  const onSearch = useCallback((params) => {
    const searchParams = {
      ...params,
    };
    setSearchParams({ ...params });
    fetchBookmarkList(searchParams);
  }, [fetchBookmarkList]);
  
  // 书签选择
  const onSelectBookMark = (selectedItem) => {
    getSelectedData(selectedItem?.[0]);
    changeSelectBookMarkVisible(false);
    // 打开新增书签弹窗
    changeAddOrEditBookmarkVisible(true);
  };

  // 新增书签
  const addBookMark = () => {
    changeMode('create');
    changeSelectBookMarkVisible(true);
  };

  // 编辑
  // const handleEdit = (record) => {
  //   changeMode('edit');
  //   getSelectedData(record);
  //   changeAddOrEditBookmarkVisible(true);
  // };
  
  // 模板引用情况
  const handleTemplateUse = (record) => {
    fetchTemplateUseList({ id: record.id });
    changeMode('templateUse');
    getSelectedData(record);
    changeAddOrEditBookmarkVisible(true);
  };

  // 启用书签
  const handleEnable = (id) => {
    fetchEnableBookmark({ id }).then(res => {
      if (res?.success) {
        message.success('启用成功');
        onSearch(newSearchParams);
      } else {
        message.error('启用失败');
      }
    });
  };
  // 停用书签
  const handleDisable = (id) => {
    fetchDisableBookmark({ id }).then(res => {
      if (res?.success) {
        message.success('停用成功');
        onSearch(newSearchParams);
      } else {
        message.error('停用失败');
      }
    });
  };

  // 删除书签
  const handleDelete = (id) => {
    fetchDeleteBookmark({ id }).then(res => {
      if (res?.success) {
        message.success('删除成功');
        onSearch(newSearchParams);
      } else {
        message.error('删除失败');
      }
    });
  };
  
  const searchItem = [
    {
      label: '书签编码',
      id: 'code',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '书签名称',
      id: 'name',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '标准字段编码',
      id: 'stdlibCode',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '标准字段名称',
      id: 'stdlibName',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '业务类型',
      id: 'bizType',
      render: () => {
        return (
          <Select options={bizTypeList} />
        );
      },
    },
  ];
  const generateActions = (buttonList, record) => {
    return buttonList?.map(button => {
      if (button.code === 'detail') {
        return {
          label: button.name,
          to: `/standardManage/bookMark/detail/${record.id}`,
        };
      }
      if (button.code === 'delete') {
        return {
          label: button.name,
          popConfirm: {
            title: '是否确认删除书签？',
            onConfirm: () => handleDelete(record.id),
          },
        };
      }
      return {
        label: button.name,
        onRowClick: () => handleAction(button.code, record),
      };
    });
  };

  const handleAction = (actionCode, record) => {
    switch (actionCode) {
      // case 'edit':
      //   handleEdit(record);
      //   break;
      case 'enable':
        handleEnable(record.id);
        break;
      case 'disable':
        handleDisable(record.id);
        break;
      case 'delete':
        break;
      case 'detail':
        break;
      case 'addChildren': //  添加子字段
        changeParentId(record.id);
        addBookMark();
        break;
      case 'showUseInfo': // 模板引用情况
        handleTemplateUse(record);
        break;
      default:
        console.warn('未知操作类型:', actionCode);
    }
  };
  const tableColumns: ColumnProps<any>[] = [
    {
      title: '书签编码',
      dataIndex: 'code',
      key: 'code',
      width: 200,
      fixed: 'left',
    },
    {
      title: '书签名称',
      dataIndex: 'name',
      key: 'name',
      width: 220,
      fixed: 'left',
      render: (text, record) => {
        return (<a href={`/digital-integrated-index/announcement-front/standardManage/bookMark/detail/${record.id}`}>{text}</a>);
      },
    },
    {
      title: '书签类型',
      dataIndex: 'type',
      key: 'type',
      width: 200,
    },
    {
      title: '标准字段名称',
      dataIndex: 'stdlibName',
      key: 'stdlibName',
      width: 220,
    },
    {
      title: '标准字段编码',
      dataIndex: 'stdlibCode',
      key: 'stdlibCode',
    },
    {
      title: '更新情况',
      dataIndex: 'update',
      key: 'update',
      render: (text, record) => {
        return (
          <div>
            <div>修改人：{record.modifierName}</div>
            <div>修改时间：{record.modifyTime}</div>
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text, record) => {
        return (stdlibStatusList?.find(i => i.value === record.status)?.label);
      },
    },   
    {
      title: '操作',
      key: 'action',
      width: 170,
      fixed: 'right',
      render: (text, record) => {
        return (
          <div className="template-bookmark-list-actions">
            <Actions
              record={record}
              maxShowSize={3}
              actions={generateActions(record.buttonList, record)}
            />
          </div>
        );
      },
    },
  ];
  
  return (
    <PaaSLayout
      globalBtn={handleButton()}
    >
      <Spin spinning={bookmarkListLoading}>
        <ZcyList
          expandForm
          initSearchParams={bookmarkInitSearchParams}
          onSearch={onSearch}
          customItem={searchItem}
          table={{
            columns: tableColumns,
            dataSource: bookmarkList,
            rowKey: 'id',
            pagination: bookmarkListPagination,
            rowSelection: undefined,
            scroll: {
              x: 1800,
            },
          }}
          openSearchCache
        />
        {
          selectBookMarkVisible && (
            <SelectBookMarkModal
              visible={selectBookMarkVisible}
              onCancel={() => changeSelectBookMarkVisible(false)}
              onSelectModalOk={(selectedItem) => {
                onSelectBookMark(selectedItem);
              }}
            />
          )
        }
        {
          addOrEditBookmarkVisible && (
            <AddOrEditBookmark
              mode={mode}
              visible={addOrEditBookmarkVisible}
              onCancel={() => changeAddOrEditBookmarkVisible(false)}
              onModalOk={() => {
                onSearch(newSearchParams);
                changeAddOrEditBookmarkVisible(false);
              }}
              formDetail={selectedData}
              templateUseList={templateUseList}
              // parentId={parentId}
              templateInfoLoading={templateInfoLoading}
            />
          )
        }
      </Spin>
    </PaaSLayout>
  );
}
