/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-20 18:01:30
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 17:38:15
 * @FilePath: /zcy-announcement-v2-front/src/routes/StanaedFieldManage/views/list.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect } from 'react';
import PaaSLayout from '@/layouts/PaaSLayout';
import { Spin, Form, FormGrid, Panel } from '@zcy/doraemon';
import { standardFieldsConfig } from '../../config/standardFIeldsConfig';
import { handleFormGrid } from '../../components/handleFormFields';
import useStandardFieldsListApi from './hooks/standardFieldsDetailApi';
import { transformedData, getLastPathParam } from '../../utils';

const featureFormItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 15 },
  column: 2,
  bordered: true,
};
export default function BookMarkDetail() {
  const {
    discList,
    fetchDictList,
    fetchStandardFieldsDetail,
    standardFieldsDetail,
    standardFieldsDetailLoading,
    logInfoList,
    logInfoTotal,
    fetchLogsInfo,
  } = useStandardFieldsListApi();

  const standardFieldId = getLastPathParam();

  useEffect(() => {
    fetchStandardFieldsDetail({ id: standardFieldId });
    fetchLogsInfo({
      bizId: standardFieldId,
      module: 'AnnouncementStdLib',
      pageNo: 1,
      pageSize: 10,
    });
    fetchDictList({ codes: ['bizType', 'stdlibStatus', 'componentType', 'stdlibTag'] });
  }, []);

  // 日志信息列表切换分页
  const handleLogTableChange = (pageNo) => {
    fetchLogsInfo({
      bizId: standardFieldId,
      module: 'AnnouncementStdLib',
      pageNo,
      pageSize: 10,
    });
  };

  const getFormItem = () => {
    const fields = standardFieldsConfig('detail');
    const record = transformedData(standardFieldsDetail, discList);
    return handleFormGrid({
      fields, 
      record,
      discList,
      logInfoList,
      logInfoTotal,
      handleLogTableChange,
    });
  };

  const breadcrumb = [
    {
      label: '标准字段配置列表',
    },
    {
      label: '详情',
    },
  ];
  
  return (
    <PaaSLayout
      routes={breadcrumb}
      envDisabled
      globalBtn={[{
        label: '返回',
        onClick: () => {
          history.back();
        },
      }]}
    >
      <Spin spinning={standardFieldsDetailLoading}>
        <Panel>
          <Form>
            <FormGrid 
              {...featureFormItemLayout} 
              formGridItem={getFormItem() || []}
            />
          </Form>
        </Panel>
      </Spin>

    </PaaSLayout>
  );
}
