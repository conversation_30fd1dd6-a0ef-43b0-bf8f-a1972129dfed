/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-27 10:00:00
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-27 10:00:00
 * @Description: 标准字段使用 API hooks
 */
import { useRequest } from '@zcy/react-hooks';
import { useEffect } from 'react';
import {
  allAnnTypeGet,
  listGet,
  deletePost,
  getAllStdlibGet,
} from 'src/api/announcement/opPlatform/prodOperation/announcement/announcementReferConfigController';
import { fetchDictList } from 'src/routes/StandardFieldManage/services';


function annTypeTreeData(allAnnTypeList: any[]) {
  for (let i = 0; i < allAnnTypeList.length; i++) {
    const item = allAnnTypeList[i];
    item.value = item.id;
    item.title = item.name;
    item.key = item.id;
    
    if (item.children && item.children.length > 0) {
      // 有子节点的禁用选择（只允许选择叶节点）
      item.disabled = true;
      annTypeTreeData(item.children);
    } else {
      // 叶节点可以选择
      item.disabled = false;
    }
  }
}
export default function useStandardFieldUseApi() {
  // 获取标准字段使用列表
  const fetchParallel = () => {
    return Promise.all([
      fetchDictList({
        codes: ['bizType', 'stdlibStatus', 'componentType', 'stdlibTag'],
      }),
      allAnnTypeGet(),
      // getAllStdlibGet(),
    ]).then(([dictRes, allAnnTypeRes]) => {
      annTypeTreeData((allAnnTypeRes as any).result || []);
      return [(dictRes as any).result, (allAnnTypeRes as any).result];
    });
  };
  const parallelRes = useRequest(fetchParallel);

  const listManage = useRequest(async (params) => ((await listGet(params)).result), {
    manual: true,
  });

  const fetchStandardFieldUseList = (params) => {
    return listGet(params);
  };
  const deleteStandardFieldUse = (params) => {
    return deletePost(params);
  };
  const fetchStandardFieldUseDetail = (params) => {
    return listGet(params);
  };
  const fetchStandardFieldUseLog = (params) => {
    return listGet(params);
  };

  useEffect(() => {
    if (parallelRes.data) {
      listManage.run(
        {
          bizType: parallelRes.data[0].bizType[0].value,
          annType: parallelRes.data[1][0].children[0].id,
        },
      );
    }
  }, [parallelRes.data]);
  return {
    parallelRes,
    listManage,
    fetchStandardFieldUseList,
    deleteStandardFieldUse,
    fetchStandardFieldUseDetail,
    fetchStandardFieldUseLog,
  };
}
