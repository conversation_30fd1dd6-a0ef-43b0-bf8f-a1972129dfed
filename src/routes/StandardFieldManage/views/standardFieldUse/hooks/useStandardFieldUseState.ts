/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-27 10:00:00
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-27 10:00:00
 * @Description: 标准字段使用状态管理 hooks
 */
import { useSetState } from 'ahooks';

export default function useStandardFieldUseState() {
  const [state, setState] = useSetState({
    // 新增/编辑弹窗显示状态
    addOrEditVisible: false,
    // 详情弹窗显示状态
    detailVisible: false,
    // 日志弹窗显示状态
    logVisible: false,
    // 复制弹窗显示状态
    copyVisible: false,
    // 选中的数据
    selectedData: null,
    // 操作模式：create-新增, edit-编辑, detail-详情, log-日志, copy-复制
    mode: 'create',
  });

  const openAddOrEditModal = (mode: string) => {
    setState({
      addOrEditVisible: true,
      mode,
    });
  };

  const closeAddOrEditModal = (mode: string) => {
    setState({
      addOrEditVisible: false,
    });
  };


  /** 控制详情弹窗是否显示 */
  const changeDetailVisible = (show: boolean) => {
    setState({
      detailVisible: show,
    });
  };

  /** 控制日志弹窗是否显示 */
  const changeLogVisible = (show: boolean) => {
    setState({
      logVisible: show,
    });
  };

  /** 控制复制弹窗是否显示 */
  const changeCopyVisible = (show: boolean) => {
    setState({
      copyVisible: show,
    });
  };

  /** 设置选中的数据 */
  const setSelectedData = (data: any) => {
    setState({
      selectedData: data,
    });
  };

  /** 设置操作模式 */
  const setMode = (mode: string) => {
    setState({ mode });
  };

  /** 重置状态 */
  const resetState = () => {
    setState({
      addOrEditVisible: false,
      detailVisible: false,
      logVisible: false,
      copyVisible: false,
      selectedData: null,
      mode: 'create',
    });
  };


  return {
    ...state,
    changeDetailVisible,
    changeLogVisible,
    changeCopyVisible,
    setSelectedData,
    setMode,
    resetState,
    openAddOrEditModal,
    closeAddOrEditModal,
  };
}
