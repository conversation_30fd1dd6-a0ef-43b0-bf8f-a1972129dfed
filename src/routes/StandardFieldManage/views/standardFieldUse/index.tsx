/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-27 10:00:00
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-27 10:00:00
 * @Description: 标准字段使用管理页面
 */
import React, { useCallback } from 'react';
import PaaSLayout from 'src/layouts/PaaSLayout';
import { ColumnProps } from '@zcy/doraemon/lib/table/interface';
import {
  Spin,
  ZcyList,
  Input,
  Select,
  Table,
  Tag,
  TreeSelect,
} from 'doraemon';
import useStandardFieldUseApi from './hooks/useStandardFieldUseApi';
import useStandardFieldUseState from './hooks/useStandardFieldUseState';
import AddOrEditModal from './components/AddOrEditModal';
import { PAGE_MODE } from '../../constants';

const { Actions } = Table;

export default function StandardFieldUse() {
  const {
    parallelRes,
    listManage,
    deleteStandardFieldUse,
    fetchStandardFieldUseDetail,
    fetchStandardFieldUseLog,
  } = useStandardFieldUseApi();

  const {
    addOrEditVisible,
    detailVisible,
    logVisible,
    copyVisible,
    selectedData,
    mode,
    changeLogVisible,
    changeCopyVisible,
    openAddOrEditModal,
    closeAddOrEditModal,
  } = useStandardFieldUseState();

  const { data: [dictData, allAnnTypeList = []] = [] } = parallelRes || {};
  const { bizType = [], stdlibStatus = [], stdlibTag = [], componentType } = dictData || {};
  const { data: listData = [] } = listManage || {};

  const initSearchParams = {
    annType: allAnnTypeList?.[0]?.children[0]?.id,
    bizType: bizType?.[0]?.value,
    pageNo: 1,
    pageSize: 10,
  };

  const onSearch = useCallback((params) => {
    const searchParams = {
      ...params,
    };
    listManage.run(searchParams);
  }, []);


  // 编辑
  const handleEdit = (record) => {
    openAddOrEditModal(PAGE_MODE.EDIT);
  };

  // 查看详情
  const handleDetail = (record) => {
    // TODO: 实现详情查看
    fetchStandardFieldUseDetail({ id: record.id });
  };

  // 删除
  const handleDelete = async (record) => {
    try {
      await deleteStandardFieldUse({ id: record.id });
      onSearch(initSearchParams);
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 查看日志
  const handleViewLog = () => {
    changeLogVisible(true);
    fetchStandardFieldUseLog({});
  };

  // 从其他公告类型复制
  const handleCopyFromOther = () => {
    changeCopyVisible(true);
  };

  // 排序
  const handleSort = () => {
    // TODO: 实现排序功能
    console.log('排序功能待实现');
  };

  const handleAction = (actionCode, record) => {
    console.log('actionCode', actionCode);
    console.log('record', record);
    switch (actionCode) {
      case 'edit':
        handleEdit(record);
        break;
      case 'detail':
        handleDetail(record);
        break;
      case 'delete':
        handleDelete(record);
        break;
      case 'childEdit':
        // 子操作编辑
        handleEdit(record);
        break;
      case 'childDetail':
        // 子操作详情
        handleDetail(record);
        break;
      default:
        console.warn('未知操作类型:', actionCode);
    }
  };

  // 处理操作按钮
  const generateActions = (buttonList, record) => {
    return (
      buttonList?.map((button) => ({
        label: button.name,
        onRowClick: () => handleAction(button.code, record),
      })) || []
    );
  };


  // 搜索项配置
  const searchItem = [
    {
      label: '业务类型',
      id: 'bizType',
      render: () => {
        return (
          <Select placeholder="请选择业务类型">
            {bizType.map((item) => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        );
      },
    },
    {
      label: '公告类型',
      id: 'annType',
      render: () => {
        return (
          <TreeSelect
            placeholder="请选择公告类型（只能选择具体类型）"
            treeData={allAnnTypeList}
            showSearch
            treeNodeFilterProp="title"
            // treeDefaultExpandedKeys={[allAnnTypeList?.[0]?.children[0]?.id]}
          />
        );
      },
    },
    {
      label: '标准字段code',
      id: 'stdlibCode',
      render: () => {
        return <Input placeholder="请输入标准字段code" />;
      },
    },
    {
      label: '标准字段名称',
      id: 'stdlibName',
      render: () => {
        return <Input placeholder="请输入标准字段名称" />;
      },
    },
    {
      label: '状态',
      id: 'referStatus',
      render: () => {
        return (
          <Select
            placeholder="请选择状态"
            options={stdlibStatus.map((item) => ({
              label: item.label,
              value: item.value,
            }))}
          />
        );
      },
    },
  ];

  // 表格列配置
  const tableColumns: ColumnProps<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 100,
      render: (text, record, index) => index + 1,
    },
    {
      title: '标准字段code',
      dataIndex: 'stdlibCode',
      key: 'stdlibCode',
      width: 150,
    },
    {
      title: '标准字段名称',
      dataIndex: 'stdlibName',
      key: 'stdlibName',
      width: 150,
      render: (text, record) => (
        <a onClick={() => handleDetail(record)}>{text}</a>
      ),
    },
    {
      title: '控件类型',
      dataIndex: 'stdlibType',
      key: 'stdlibType',
      width: 120,
      render: (text, record) => {
        return componentType.find((item) => item.value === text)?.label;
      },
    },
    {
      title: '是否用于公告推送',
      dataIndex: 'forAnnPush',
      key: 'forAnnPush',
      width: 140,
      render: (text) => (
        <Tag color={text ? 'green' : 'red'}>{text ? '是' : '否'}</Tag>
      ),
    },
    {
      title: '是否用于表单渲染',
      dataIndex: 'forFormRender',
      key: 'forFormRender',
      width: 140,
      render: (text) => (
        <Tag color={text ? 'green' : 'red'}>{text ? '是' : '否'}</Tag>
      ),
    },
    {
      title: '别名',
      dataIndex: 'alias',
      key: 'alias',
      width: 120,
    },
    {
      title: '更新情况',
      key: 'modifySituation',
      render: (text, record) => {
        return (
          <>
            <div>更新人：{record.modifyName}</div>
            <div>更新时间：{record.modifyTime}</div>
          </>
        );
      },
      width: 160,
    },
    {
      title: '状态',
      dataIndex: 'referStatus',
      key: 'referStatus',
      width: 100,
      render: (text) => (
        <Tag color={text === 1 ? 'green' : 'red'}>
          {stdlibStatus.find((item) => item.value === text)?.label}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (text, record) => {
        return (
          <Actions
            record={record}
            maxShowSize={3}
            actions={generateActions(record.buttonList, record)}
          />
        );
      },
    },
  ];

  return (
    <PaaSLayout
      globalBtn={[
        {
          label: '新增',
          type: 'primary',
          onClick: () => openAddOrEditModal(PAGE_MODE.CREATE),
        },
        {
          label: '排序',
          onClick: () => handleSort(),
        },
        {
          label: '查看日志',
          onClick: () => handleViewLog(),
        },
        {
          label: '从其他公告类型复制',
          onClick: () => handleCopyFromOther(),
        },
      ]}
    >
      <Spin spinning={parallelRes.loading}>
        <ZcyList
          expandForm
          initSearchParams={initSearchParams}
          onSearch={onSearch}
          customItem={searchItem}
          table={{
            columns: tableColumns,
            dataSource: listData || [],
            rowKey: 'id',
            pagination: {
              showSizeChanger: true,
              total: (listManage.data as any)?.total,
              pageSize: (listManage.data as any)?.pageSize,
              current: (listManage.data as any)?.pageNum,
            },
            scroll: {
              x: 1480,
              y: 1116,
            },
          }}
          // openSearchCache
        />

        {/* 新增/编辑弹窗 */}
        {addOrEditVisible && (
          <AddOrEditModal
            visible={addOrEditVisible}
            mode={mode}
            formDetail={selectedData}
            bizType={bizType}
            allAnnTypeList={allAnnTypeList}
            stdlibTag={stdlibTag}
            componentType={componentType}
            onCancel={() => {
              closeAddOrEditModal(mode);
            }}
            onOk={() => {
              closeAddOrEditModal(mode);
              // onSearch({
              //   pageNo: 1,
              //   pageSize: 10,
              //   annType: allAnnTypeList?.[0]?.children[0]?.id,
              //   bizType: bizType?.[0]?.value,
              // });
            }}
          />
        )}

        {/* 详情弹窗 */}
        {detailVisible && <div>详情弹窗 - 待实现</div>}

        {/* 日志弹窗 */}
        {logVisible && <div>日志弹窗 - 待实现</div>}

        {/* 复制弹窗 */}
        {copyVisible && <div>复制弹窗 - 待实现</div>}
      </Spin>
    </PaaSLayout>
  );
}
