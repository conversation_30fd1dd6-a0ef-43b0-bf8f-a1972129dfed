/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-27 15:25:04
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-30 16:51:52
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useRequest } from '@zcy/react-hooks';
import {
  fetchBookmarkDetail,
  fetchLogsInfo,
  fetchDictList,
} from '../../../services';

export default function useBookMarkApi() {
  // 获取数据字典
  const discListRes = useRequest(
    (params) => {
      return fetchDictList(params);
    },
    {
      manual: true,
    }
  );
  const discListResult = discListRes?.data?.result;

  const bookmarkDetailRes = useRequest(
    (params) => {
      return fetchBookmarkDetail(params);
    },
    {
      manual: true,
    }
  );
  const bookmarkDetailResult = bookmarkDetailRes?.data?.result;
  // 日志信息
  const logInfoRes = useRequest(
    (params) => {
      return fetchLogsInfo(params);
    },
    {
      manual: true,
    }
  );
  const logInfoResult = logInfoRes?.data?.result?.data;
  const logInfoTotal = logInfoRes?.data?.result?.total;
  return {
    discList: discListResult,
    fetchDictList: discListRes.runAsync,
    fetchBookmarkDetail: bookmarkDetailRes.runAsync,
    bookmarkDetail: bookmarkDetailResult || {},
    bookmarkDetailLoading: bookmarkDetailRes.loading,
    logInfoList: logInfoResult,
    logInfoTotal,
    fetchLogsInfo: logInfoRes.runAsync,
  };
}

