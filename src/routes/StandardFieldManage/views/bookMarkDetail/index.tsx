/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-20 18:01:30
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 16:44:54
 * @FilePath: /zcy-announcement-v2-front/src/routes/StanaedFieldManage/views/list.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect } from 'react';
import { Spin, Form, FormGrid, Panel } from '@zcy/doraemon';
import PaaSLayout from '@/layouts/PaaSLayout';
import { bookMarkFieldsConfig } from '../../config/bookMarkFieldsConfig';
import { handleFormGrid } from '../../components/handleFormFields';
import useBookMarkApi from './hooks/bookMarkDetailApi';
import { transformedData, getLastPathParam } from '../../utils';

const featureFormItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 15 },
  column: 2,
  bordered: true,
};
export default function BookMarkDetail() {
  const {
    discList,
    fetchDictList,
    fetchBookmarkDetail,
    bookmarkDetail,
    bookmarkDetailLoading,
    logInfoList,
    logInfoTotal,
    fetchLogsInfo,
  } = useBookMarkApi();

  const bookMarkId = getLastPathParam();

  useEffect(() => {
    fetchDictList({ codes: ['bizType'] });
    fetchBookmarkDetail({ id: bookMarkId });
    fetchLogsInfo({
      bizId: bookMarkId,
      module: 'AnnouncementBookmark',
      pageNo: 1,
      pageSize: 10,
    });
  }, []);

  // 日志信息列表切换分页
  const handleLogTableChange = (pageNo) => {
    fetchLogsInfo({
      bizId: bookMarkId,
      module: 'AnnouncementBookmark',
      pageNo,
      pageSize: 10,
    });
  };
    
  const getFormItem = () => {
    const fields = bookMarkFieldsConfig('detail');
    const record = transformedData(bookmarkDetail, discList);
    return handleFormGrid({
      fields, 
      record,
      logInfoList,
      logInfoTotal,
      handleLogTableChange,
    });
  };
  const breadcrumb = [
    {
      label: '书签配置列表',
      to: '/standardManage/bookMark/list',
    },
    {
      label: '详情',
    },
  ];
  
  return (
    <PaaSLayout
      routes={breadcrumb}
      envDisabled
      globalBtn={[{
        label: '返回',
        onClick: () => {
          history.back();
        },
      }]}
    >
      <Spin spinning={bookmarkDetailLoading}>
        <Panel>
          <Form>
            <FormGrid 
              {...featureFormItemLayout} 
              formGridItem={getFormItem() || []}
            />
          </Form>
        </Panel>
      </Spin>
    </PaaSLayout>
  );
}
