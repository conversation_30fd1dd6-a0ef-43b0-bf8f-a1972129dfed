/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-20 18:01:30
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 14:51:48
 * @FilePath: /zcy-announcement-v2-front/src/routes/StanaedFieldManage/views/list.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useCallback } from 'react';
import PaaSLayout from '@/layouts/PaaSLayout';
import { ColumnProps } from '@zcy/doraemon/lib/table/interface';
import { Spin, ZcyList, Input, Select, Table, message } from '@zcy/doraemon';
import useStandardFieldsApi from './hooks/useStandardFieldsApi';
import useStandardFieldsState from './hooks/useStandardFieldsState';
import AddOrEditStandardFields from '../../components/AddOrEditStandardFields';

const { Actions } = Table;
export default function StandardList() {
  const BUTTON_CONFIG = {
    // 新增标准字段
    addStdlib: {
      type: 'primary',
      onClick: () => addStandardFields(),
    },
  };

  const {
    discList, // 静态字典
    fetchDictList,
    standardFieldsList,
    standardFieldsPagination,
    standardFieldsLoading,
    fetchStandardFieldsList,
    templateUseList,
    templateInfoLoading,
    fetchTemplateUseList,
    fetchEnableStandardFields,
    fetchDisableStandardFields,
    fetchDeleteStandardFields,
    fetchStandardFieldsDetail,
    standardFieldsDetailLoading,
    mainButtonList,
    fetchButtonList,
  } = useStandardFieldsApi();

  const {
    bizType: bizTypeList = [], // 业务类型
    stdlibStatus: stdlibStatusList = [], // 状态
    componentType: componentTypeList = [], // 控件类型
    stdlibTag: stdlibTagList = [], // 标准字段标签
  } = discList;
  const {
    addOrEditStandardFieldsVisible,
    changeAddOrEditStandardFieldsVisible,
    selectedData,
    mode,
    changeMode,
    parentId,
    changeParentId,
    newSearchParams,
    setSearchParams,
    getSelectedData,
    getReboundData,
  } = useStandardFieldsState();

  const initSearchParmas = {
    pageNo: 1,
    pageSize: 20,
    bizType: 1, // 政采业务
  };

  // 全局按钮
  const handleButton = useCallback(() => {
    return mainButtonList?.map(btn => {
      const config = BUTTON_CONFIG[btn.code];
      return config ? {
        ...config,
        label: btn?.name,
      } : null;
    }).filter(Boolean);
  }, [mainButtonList]);

  useEffect(() => {
    // 数据字典
    fetchDictList({ codes: ['bizType', 'stdlibStatus', 'componentType', 'stdlibTag'] });
    // 获取全局按钮列表
    fetchButtonList({ module: 'stdlib' });
  }, []);

  // 搜索
  const onSearch = (params) => {
    const searchParams = {
      ...params,
    };
    setSearchParams({ ...params });
    fetchStandardFieldsList(searchParams);
  };

  // 新增标准字段
  const addStandardFields = () => {
    // 初始化数据
    getReboundData({});
    changeMode('create');
    changeAddOrEditStandardFieldsVisible(true);
  };

  // 编辑
  const handleEdit = async (record) => {
    changeMode('edit');
    const detail = await fetchStandardFieldsDetail({ id: record.id });
    getReboundData(detail?.result);
    changeAddOrEditStandardFieldsVisible(true);
  };
  
  // 模板引用情况
  const handleTemplateUse = (record) => {
    fetchTemplateUseList({ id: record.id });
    getSelectedData(record, discList);
    changeMode('templateUse');
    changeAddOrEditStandardFieldsVisible(true);
  };

  // 启用标准字段
  const handleEnable = (id) => {
    fetchEnableStandardFields({ id }).then(res => {
      if (res?.success) {
        message.success('启用成功');
        onSearch(newSearchParams);
      } else {
        message.error('启用失败');
      }
    });
  };
  // 停用标准字段
  const handleDisable = (id) => {
    fetchDisableStandardFields({ id }).then(res => {
      if (res?.success) {
        message.success('停用成功');
        onSearch(newSearchParams);
      } else {
        message.error('停用失败');
      }
    });
  };

  // 删除标准字段
  const handleDelete = (id) => {
    fetchDeleteStandardFields({ id }).then(res => {
      if (res?.success) {
        message.success('删除成功');
        onSearch(newSearchParams);
      } else {
        message.error('删除失败');
      }
    });
  };
  
  const searchItem = [
    {
      label: '标准字段编码',
      id: 'code',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '标准字段名称',
      id: 'name',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '状态',
      id: 'status',
      render: () => {
        return (<Select allowClear options={stdlibStatusList} />);
      },
    }, 
    {
      label: '业务类型',
      id: 'bizType',
      render: () => {
        return (
          <Select options={bizTypeList} />
        );
      },
    },
  ];
  const generateActions = (buttonList, record) => {
    return buttonList?.map(button => {
      if (button.code === 'detail') {
        return {
          label: button.name,
          to: `/standardManage/standardFields/detail/${record.id}`,
        };
      } 
      if (button.code === 'delete') {
        return {
          label: button.name,
          popConfirm: {
            title: '是否确认删除标准字段？',
            onConfirm: () => handleDelete(record.id),
          },
        };
      }
      return {
        label: button.name,
        onRowClick: () => handleAction(button.code, record),
      };
    });
  };

  const handleAction = (actionCode, record) => {
    switch (actionCode) {
      case 'edit':
        handleEdit(record);
        break;
      case 'enable':
        handleEnable(record.id);
        break;
      case 'disable':
        handleDisable(record.id);
        break;
      case 'delete':
        break;
      case 'detail':
        break;
      case 'addChildren': //  添加子字段
        changeParentId(record.id);
        addStandardFields();
        break;
      case 'showUseInfo': // 模板引用情况
        handleTemplateUse(record);
        break;
      default:
        console.warn('未知操作类型:', actionCode);
    }
  };
  const tableColumns: ColumnProps<any>[] = [
    {
      title: '标准字段编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      fixed: 'left',
    },
    {
      title: '标准字段名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      fixed: 'left',
      render: (text, record) => {
        return (<a href={`/digital-integrated-index/announcement-front/standardManage/standardFields/detail/${record.id}`}>{text}</a>);
      },
    },
    {
      title: '控件类型',
      dataIndex: 'componentType',
      key: 'componentType',
      width: 180,
      render: (text, record) => {
        return (componentTypeList?.find(i => i.value === record.componentType)?.label);
      },
    },
    {
      title: '是否独占一行',
      dataIndex: 'ifRow',
      key: 'ifRow',
      width: 130,
      render: (text, record) => {
        return (text ? '是' : '否');
      },
    },
    {
      title: '是否必填',
      dataIndex: 'ifMust',
      key: 'ifMust',
      width: 130,
      render: (text, record) => {
        return (text ? '是' : '否');
      },
    },
    {
      title: '更新情况',
      dataIndex: 'update',
      key: 'update',
      render: (text, record) => {
        return (
          <div>
            <div>修改人：{record.modifierName}</div>
            <div>修改时间：{record.modifyTime}</div>
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (text, record) => {
        return (stdlibStatusList?.find(i => i.value === record.status)?.label);
      },
    },   
    {
      title: '操作',
      key: 'action',
      width: 170,
      fixed: 'right',
      render: (text, record) => {
        return (
          <div className="template-bookmark-list-actions">
            <Actions
              record={record}
              maxShowSize={3}
              actions={generateActions(record.buttonList, record)}
            />
          </div>
        );
      },
    },
  ];
  
  return (
    <PaaSLayout
      globalBtn={handleButton()}
    >
      <Spin spinning={standardFieldsLoading}>
        <ZcyList
          expandForm
          initSearchParams={initSearchParmas}
          onSearch={onSearch}
          customItem={searchItem}
          table={{
            columns: tableColumns,
            dataSource: standardFieldsList,
            rowKey: 'id',
            pagination: standardFieldsPagination,
            rowSelection: undefined,
            scroll: {
              x: 1400,
            },
          }}
          openSearchCache
        />
        {
          addOrEditStandardFieldsVisible && (
            <AddOrEditStandardFields
              mode={mode}
              visible={addOrEditStandardFieldsVisible}
              onCancel={() => changeAddOrEditStandardFieldsVisible(false)}
              onModalOk={() => {
                onSearch(newSearchParams);
                changeAddOrEditStandardFieldsVisible(false);
              }}
              formDetail={selectedData}
              templateUseList={templateUseList}
              parentId={parentId}
              componentTypeList={componentTypeList}
              tagList={stdlibTagList}
              discList={discList}
              templateInfoLoading={templateInfoLoading}
              editModalLoading={standardFieldsDetailLoading}
            />
          )
        }
      </Spin>
    </PaaSLayout>
  );
}
