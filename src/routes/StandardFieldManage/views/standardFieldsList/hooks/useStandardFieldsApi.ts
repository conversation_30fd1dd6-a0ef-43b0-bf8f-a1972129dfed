/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 16:40:18
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-05 20:23:27
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useRequest } from '@zcy/react-hooks';
import {
  fetchDictList,
  fetchStandardFieldList,
  fetchTemplateUseArray,
  fetchEnableStandardFields,
  fetchDisableStandardFields,
  fetchDeleteStandardFields,
  fetchStandardFieldsDetail,
  fetchButtonList,
} from '../../../services';

export default function useStandardFieldsApi() {
// 获取数据字典
  const discListRes = useRequest(
    (params) => {
      return fetchDictList(params);
    },
    {
      manual: true,
    }
  );
  const discListResult = discListRes?.data?.result;

  // 获取书签列表
  const standardFieldsRes = useRequest(
    (params) => {
      return fetchStandardFieldList(params);
    },
    {
      manual: true,
    });
  const standardFieldsResult = standardFieldsRes?.data?.result || [];

  // 显示使用书签和模板
  const templateUseListRes = useRequest(
    (params) => {
      return fetchTemplateUseArray(params);
    },
    {
      manual: true,
    }
  ) || [];
  const templateUseListResult = templateUseListRes?.data?.result?.flatMap(item => 
    item.templates.map(template => ({
      bookmark: item.bookmark,
      templateCode: template.templateCode,
      templateName: template.templateName,
      templateId: template.templateId,
    }))
  );

  // 启用书签
  const enableStandardFieldsRes = useRequest(
    (params) => {
      return fetchEnableStandardFields(params);
    },
    {
      manual: true,
    }
  );

  // 停用书签
  const disableStandardFieldsRes = useRequest(
    (params) => {
      return fetchDisableStandardFields(params);
    },
    {
      manual: true,
    }
  );

  // 删除书签
  const deleteStandardRes = useRequest(
    (params) => {
      return fetchDeleteStandardFields(params);
    },
    {
      manual: true,
    }
  );

  // 获取详情
  const standardFieldsDetailRes = useRequest(
    (params) => {
      return fetchStandardFieldsDetail(params);
    },
    {
      manual: true,
    }
  );
  const standardFieldsDetailResult = standardFieldsDetailRes?.data?.result;

  // 获取主按钮
  const buttonListRes = useRequest(
    (params) => {
      return fetchButtonList(params);
    },
    {
      manual: true,
    }
  );
  const mainButtonList = buttonListRes?.data?.result?.buttonList;

  return {
    discList: discListResult,
    fetchDictList: discListRes.runAsync,
    standardFieldsList: standardFieldsResult?.data || [],
    standardFieldsPagination: {
      showSizeChanger: true,
      total: standardFieldsResult?.total,
      pageSize: standardFieldsResult?.pageSize,
      pageNo: standardFieldsResult?.pageNo,
    },
    standardFieldsLoading: standardFieldsRes.loading,
    fetchStandardFieldsList: standardFieldsRes.runAsync,
    templateUseList: templateUseListResult || [],
    fetchTemplateUseList: templateUseListRes.runAsync,
    templateInfoLoading: templateUseListRes.loading,
    fetchEnableStandardFields: enableStandardFieldsRes.runAsync,
    fetchDisableStandardFields: disableStandardFieldsRes.runAsync,
    fetchDeleteStandardFields: deleteStandardRes.runAsync,
    standardFieldsDetail: standardFieldsDetailResult || {},
    standardFieldsDetailLoading: standardFieldsDetailRes.loading,
    fetchStandardFieldsDetail: standardFieldsDetailRes.runAsync,
    mainButtonList,
    fetchButtonList: buttonListRes.runAsync,
  };
}
