/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-28 17:36:22
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-04 17:10:16
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useSetState } from '@zcy/react-hooks';
import { transformedData } from '../../../utils';

export default function useBookmarkState() {
  const [state, setState] = useSetState({
    addOrEditStandardFieldsVisible: false,
    selectBookMarkVisible: false,
    selectedData: [],
    mode: 'create',
    parentId: '',
    newSearchParams: {
    },
  });

  /** 控制新增编辑框是否显示 */
  const changeAddOrEditStandardFieldsVisible = (show: boolean) => {
    setState({
      addOrEditStandardFieldsVisible: show,
    });
  };

  const changeMode = (mode: string) => {
    setState({ mode });
  };
  
  const changeParentId = (id) => {
    setState({
      parentId: id,
    });
  };
  const setSearchParams = (params) => {
    setState({
      newSearchParams: params,
    });
  };

  const getSelectedData = (data, discList = []) => {
    const curData = transformedData(data, discList);
    setState({
      selectedData: curData,
    });
  };
  const getReboundData = (data) => {
    setState({
      selectedData: data,
    });
  };
  
  return {
    ...state,
    changeAddOrEditStandardFieldsVisible,
    changeMode,
    changeParentId,
    setSearchParams,
    getSelectedData,
    getReboundData,
  };
}
