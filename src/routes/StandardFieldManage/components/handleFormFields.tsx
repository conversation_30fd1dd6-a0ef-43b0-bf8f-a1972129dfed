import React from 'react';
import { Input, Select, Radio, Table, InputNumber, TreeSelect } from '@zcy/doraemon';
import { transformedData } from '../utils';

const { Option } = Select;
const RadioGroup = Radio.Group;
const { TextArea } = Input;

export const handleFormFields = (props) => {
  const { 
    fields, 
    record, 
    form, 
    handleSearch, 
    searchOptions = [], 
    handleStdlibNameChange,
  } = props;

  const { getFieldDecorator, getFieldsValue } = form;
  const formValues = getFieldsValue();
  return fields
    .filter((field) => {
      // 基础可见性过滤
      if (!field.visible) return false;
      // 处理字段联动显示逻辑
      if (field.relateTo) {
        const { name, value } = field.relateTo;
        const relatedValue = formValues?.[name] || record?.[name];
        return relatedValue === value;
      }
      return true;
    }) // 过滤掉不需要显示的字段
    .map(field => ({
      label: field.label,
      extra: field.extra,
      style: field.isHidden ? { display: 'none' } : {},
      render: () => {
        // 表单控件配置
        const decoratorOptions = {
          rules: field.rules ? field.rules : [],
          initialValue: record?.[field.name],
          ...(field.isHidden ? { style: { display: 'none' } } : {}), // 新增隐藏判断
          // 特殊处理 stdlibName 字段
          ...(field.name === 'stdlibName' ? {
            getValueFromEvent: (value, option) => {
              // 返回选中的 label
              return option?.props?.children || value;
            },
          } : {}),
        };
        // 根据不同类型渲染不同控件
        let formControl;
        switch (field.type) {
          case 'input':
            formControl = (
              <Input 
                placeholder={field?.placeholder} 
                disabled={field?.disabled}
              />
            );
            break;
          case 'inputNumber':
            formControl = (
              <InputNumber 
                placeholder={field?.placeholder} 
                disabled={field?.disabled}
                {...field?.extraParams}
              />
            );
            break;
          case 'textArea':
            formControl = (
              <TextArea 
                placeholder={field?.placeholder} 
                disabled={field?.disabled}
              />
            );
            break;
          case 'select':
            // 需要远程搜索
            const searchParams = field?.isNeedSearch ? (
              {
                showSearch: true,
                onSearch: handleSearch,
                filterOption: false,
                optionFilterProp: 'children',
              }
            ) : {};
              // 是否多选参数
            const searchParmas2 = field?.isMultiple ? (
              {
                mode: 'multiple',
              }
            ) : {};
              // 如果是标准字段名称stdlibName(标准字段名称)，添加onChange事件，给标准字段编码赋值
            const selectProps = field.name === 'stdlibName' ? {
              ...searchParams,
              onChange: handleStdlibNameChange,
            } : {};
            const options = field?.isNeedSearch ? searchOptions : field.options;
            formControl = (
              <Select 
                placeholder={field?.placeholder}
                disabled={field?.disabled}
                {...selectProps}
                {...searchParmas2}
              >
                {options?.map(option => (
                  <Option key={option.id} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            );
            break;
          case 'radio':
            formControl = (
              <RadioGroup 
                options={field.options} 
              />
            );
            break;
            // 可以继续添加其他类型的控件
          case 'treeSelect':
            formControl = (
              <TreeSelect
                placeholder={field?.placeholder}
                treeData={field?.treeData}
                showSearch={field?.showSearch}
                treeNodeFilterProp={field?.treeNodeFilterProp}
              />
            );
            break;
          default:
            formControl = <Input placeholder={field.placeholder} />;
        }
    
        return (getFieldDecorator(field.name, decoratorOptions)(formControl));
      },
    }));
};

// 渲染模板引用情况弹窗和详情页
export const handleFormGrid = (props) => {
  const { 
    fields, 
    record, 
    templateUseList, 
    logInfoList, 
    logInfoTotal,
    handleLogTableChange,
    discList,
  } = props;
  return fields
    .filter((field) => {
      if (field.name === 'children' && !record?.children?.length) {
        return false;
      }
      // 条件1：字段可见性为true 条件2：字段值存在（非undefined/null/空数组）条件3：字段类型为table
      // 条件1 &&(条件2 || 条件3)
      return field?.visible && ((
        record?.[field.name] !== undefined && 
        record?.[field.name] !== null &&
        !(Array.isArray(record?.[field.name]) && record?.[field.name].length === 0)
      ) || (field?.visible && field.type === 'table'));
    })
    .map(field => ({
      label: field.label,
      colSpan: field.colSpan || 1,
      render: () => {
        // 配置字段类型为table,渲染表格
        if (field.type === 'table') {
          let dataSource = [];
          // 配置字段名称为templateUse
          if (field.name === 'templateUse') {
            dataSource = templateUseList;
          } else if (field.name === 'logsInfo') {
            dataSource = logInfoList;
          } else {
            const data = record?.[field.name];
            const curData = data?.map(i => {
              return transformedData(i, discList);
            });
            dataSource = curData || [];
          }
          const getPaginationParams = () => {
            // 如果配置需要页码
            if (field.ifPagination) {
              // 如果是模板引用情况
              if (field.name === 'logsInfo') {
              // 如果是日志信息
                return {
                  total: logInfoTotal,
                  onChange: handleLogTableChange,
                };
              }
            } else { // 配置不需要页面
              return false;
            }
          };
          return (
            <Table 
              columns={field.columns}
              dataSource={dataSource}
              pagination={getPaginationParams()}
            />
          );
        }
        // 渲染字段值
        return <span>{record?.[field.name] || '-'}</span>;
      },
    }));
};
