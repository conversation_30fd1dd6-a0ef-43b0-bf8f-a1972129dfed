/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-22 20:17:08
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 10:38:26
 */
import React from 'react';
import { Form, Modal, FormGrid, message, Spin } from '@zcy/doraemon';
import { ColumnType } from '@zcy/doraemon/lib/form-grid/interface';
import { FormMode, Template } from '../../types';
import useAddOrEditStandardFieldsApi from './hooks/useAddOrEditStandardFieldsApi';
import { standardFieldsConfig } from '../../config/standardFIeldsConfig';
import { handleFormFields, handleFormGrid } from '../../components/handleFormFields';
import { STANDARD_TITLE, PAGE_MODE } from '../../constants';

interface AddOrEditStandardFieldsProps {
  mode: FormMode;
  visible: boolean;
  formDetail: [];
  form: any;
  tableData: [];
  parentId: Number;
  onCancel: () => void;
  onModalOk: (values: Template) => void;
}
const featureFormItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 15 },
  column: 1 as ColumnType,
  bordered: false,
};

const AddOrEditStandardFields: React.FC<AddOrEditStandardFieldsProps> = (props) => {
  const { 
    form, 
    mode, 
    visible, 
    onCancel, 
    onModalOk, 
    formDetail, 
    tableData, 
    parentId, 
    componentTypeList = [],
    tagList = [],
    templateUseList,
    templateInfoLoading,
    editModalLoading,
  } = props;
  const { validateFields } = form;
  const {
    updateStandardFieldsRes,
  } = useAddOrEditStandardFieldsApi();

  const onSubmit = () => {
    // 模板引用弹窗确认
    if (mode === PAGE_MODE.TEMPLATEUSE) {
      onModalOk?.();
      return;
    }
    // 新增和编辑弹窗确认
    validateFields((error, values) => {
      if (error) {
        return;
      }
      const params = {
        ...values,
        parentId: formDetail?.parentId || parentId,
        id: formDetail?.id,
        version: formDetail?.version,
      };
      updateStandardFieldsRes(params).then((res) => { 
        if (res?.success) {
          if (mode === PAGE_MODE.CREATE) {
            message.success('新增成功');
          } else {
            message.success('编辑成功');
          }
          onModalOk?.();
          return;
        } 
        if (mode === PAGE_MODE.CREATE) {
          message.error(res?.error || res?.message || '新增失败');
        } else {
          message.error(res?.error || res?.message || '编辑失败');
        }
      });
    });
  };

  const getFormItem = (record?: any) => {
    const originFields = standardFieldsConfig(mode);

    const fields = originFields.map((i) => {
      if (i.name === 'componentType') {
        return {
          ...i,
          options: componentTypeList,
        };
      } else if (i.name === 'tags') {
        return {
          ...i,
          options: tagList,
        };
      }
      return i;
    });
    if (mode === PAGE_MODE.CREATE || mode === PAGE_MODE.EDIT) {
      return handleFormFields({
        fields, 
        record, 
        form, 
        tableData,
      });
    } else if (mode === PAGE_MODE.TEMPLATEUSE) {
      return handleFormGrid({
        fields, 
        record, 
        form, 
        tableData,
        templateUseList,
      });
    }
  };

  const handleLoading = () => {
    if (mode === PAGE_MODE.TEMPLATEUSE) {
      return templateInfoLoading;
    } else if (mode === PAGE_MODE.EDIT) {
      return editModalLoading;
    }
    return false;
  };

  return (
    <Modal
      title={STANDARD_TITLE[mode]}
      visible={visible}
      width={mode === PAGE_MODE.TEMPLATEUSE ? 800 : 600}
      isAdaptable={false}
      onOk={onSubmit}
      onCancel={onCancel}
    >
      <Spin spinning={handleLoading()}>
        <Form>
          <FormGrid 
            {...featureFormItemLayout} 
            formGridItem={getFormItem(formDetail) || []} 
          />
        </Form>
      </Spin>
    </Modal>
  );
};
export default Form.create()<AddOrEditStandardFieldsProps>(AddOrEditStandardFields);
