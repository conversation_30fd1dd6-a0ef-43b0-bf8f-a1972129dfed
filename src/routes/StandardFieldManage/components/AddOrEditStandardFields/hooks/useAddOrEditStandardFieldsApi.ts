/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-23 13:44:23
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-29 10:31:33
 */
import { useRequest } from '@zcy/react-hooks';
import {
  updateStandardField,
} from '../../../services';

export default function useAddOrEditStandardFieldsApi() {
  // 新增书签
  const updateStandardFieldsRes = useRequest(
    (params) => {
      return updateStandardField({ ...params });
    },
    {
      manual: true,
    }
  );
  
  return {
    updateStandardFieldsRes: updateStandardFieldsRes.runAsync,
    updateStandardFieldsLoading: updateStandardFieldsRes.loading,
  };
}
