/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-23 13:44:23
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 10:05:48
 */
import { useRequest } from '@zcy/react-hooks';
import {
  updateTemplateBookmarkApi,
  fetchStandardFieldList,
} from '../../../services';

export default function useAddOrEditBookmarkApi() {
  // 新增书签
  const updateTemplateBookmarkRes = useRequest(
    (params) => {
      return updateTemplateBookmarkApi({ ...params });
    },
    {
      manual: true,
    }
  );

  // 获取获取标准字段列表
  const standardFieldListRes = useRequest(fetchStandardFieldList, {
    manual: true,
  });
  const standardFieldListResult = standardFieldListRes?.data?.result.data || [];
  const standardFieldList = standardFieldListResult.map((item) => {
    return {
      label: item.name,
      value: item.code,
      id: item.id,
    };
  });
    
  
  return {
    updateTemplateBookmark: updateTemplateBookmarkRes.runAsync,
    updateTemplateBookmarkLoading: updateTemplateBookmarkRes.loading,
    // 标准字段列表
    standardFieldList,
    fetchStandardFieldList: standardFieldListRes.runAsync,
  };
}
