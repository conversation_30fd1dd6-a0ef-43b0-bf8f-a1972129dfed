/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-22 20:17:08
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 11:21:16
 */
import React from 'react';
import _ from 'lodash';
import { Form, Modal, FormGrid, message, Spin } from '@zcy/doraemon';
import { ColumnType } from '@zcy/doraemon/lib/form-grid/interface';
import { FormMode, Template } from '../../types';
import useAddOrEditBookmarkApi from './hooks/useAddOrEditBookmarkApi';
import { bookMarkFieldsConfig } from '../../config/bookMarkFieldsConfig';
import { handleFormFields, handleFormGrid } from '../../components/handleFormFields';
import { BOOKMARK_MODAL_TITLE, PAGE_MODE } from '../../constants';

interface AddOrEditBookmarkProps {
  mode: FormMode;
  visible: boolean;
  formDetail: [];
  form: any;
  templateUseList: [];
  templateInfoLoading: boolean;
  onCancel: () => void;
  onModalOk: (values: Template) => void;
}

const featureFormItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 15 },
  column: 1 as ColumnType,
  bordered: false,
};

const AddOrEditBookmark: React.FC<AddOrEditBookmarkProps> = (props) => {
  const { 
    form, 
    mode, 
    visible, 
    onCancel, 
    onModalOk, 
    formDetail, 
    templateUseList, 
    templateInfoLoading,
  } = props;
  const { validateFields, setFieldsValue } = form;
  const {
    updateTemplateBookmark,
    updateTemplateBookmarkLoading,
    fetchStandardFieldList,
    standardFieldList,
  } = useAddOrEditBookmarkApi();

  const confirmLoading = updateTemplateBookmarkLoading;

  const onSubmit = () => {
    // 模板引用弹窗确认
    if (mode === PAGE_MODE.TEMPLATEUSE) {
      onModalOk?.();
      return;
    }
    // 新增和编辑弹窗确认
    validateFields((error, values) => {
      if (error) {
        return;
      }
      const params = {
        ...values,
      };
      updateTemplateBookmark(params).then((res) => { 
        if (res?.success) {
          if (mode === PAGE_MODE.CREATE) {
            message.success('新增成功');
          } else {
            message.success('编辑成功');
          }
          onModalOk?.();
          return;
        }
        if (mode === PAGE_MODE.CREATE) {
          message.error(res?.error || res?.message || '新增失败');
        } else {
          message.error(res?.error || res?.message || '编辑失败');
        }
      });
    });
  };

  const onSearchStandardField = (newVal) => { 
    fetchStandardFieldList({ 
      name: newVal,
      pageNo: 1,
      pageSize: 10,

    });
  };

  const handleStdlibNameChange = (value, option) => {
    setFieldsValue({ stdlibId: option.key });
    setFieldsValue({ stdlibCode: value });
  };
  
  const getFormItem = (record?: any) => {
    const fields = bookMarkFieldsConfig(mode);
    if (mode === PAGE_MODE.CREATE || mode === PAGE_MODE.EDIT) {
      return handleFormFields({
        fields, 
        record, 
        form, 
        handleSearch: _.debounce(v => onSearchStandardField(v), 100),
        searchOptions: standardFieldList,
        handleStdlibNameChange,
      });
    } else if (mode === PAGE_MODE.TEMPLATEUSE) {
      return handleFormGrid({
        fields, 
        record, 
        form, 
        templateUseList,
      });
    }
  };
  const handleLoading = () => {
    if (mode === PAGE_MODE.TEMPLATEUSE) {
      return templateInfoLoading;
    }
    return false;
  };

  return (
    <Modal
      title={BOOKMARK_MODAL_TITLE[mode]}
      visible={visible}
      width={mode === PAGE_MODE.TEMPLATEUSE ? 800 : 600}
      isAdaptable={false}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
    >
      <Spin spinning={handleLoading()}>
        <Form>
          <FormGrid 
            {...featureFormItemLayout} 
            formGridItem={getFormItem(formDetail) || []} 
          />
        </Form>
      </Spin>
    </Modal>
  );
};
export default Form.create()<AddOrEditBookmarkProps>(AddOrEditBookmark);
