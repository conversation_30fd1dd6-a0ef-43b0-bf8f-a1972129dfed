/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-23 15:38:48
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 15:05:20
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect } from 'react';
import { ZcyList, Spin, Input } from '@zcy/doraemon';
import Modal from '@zcy/doraemon/es/modal';
import useSelectBookMarkApi from './hooks/useSelectBookMarkApi';
import useSelectedItemState from './hooks/useSelectedItemState';

interface SelectBookMarkModalProps {
  visible: boolean;
  onCancel: () => void;
  onSelectModalOk: (selectedItem: {}) => void;
}
const SelectBookMarkModal: React.FC<SelectBookMarkModalProps> = (props) => { 
  const { visible, onCancel, onSelectModalOk } = props;
  const {
    selectedItem,
    changeSelectedItem,
  } = useSelectedItemState();

  const {
    modalBookmarkList,
    modalBookmarkListLoading,
    fetchModalBookmarkList,
    modalBookmarkListPagination,
  } = useSelectBookMarkApi();

  useEffect(() => {
    fetchModalBookmarkList();
  }, []);

  const onSearch = (params) => {
    const searchParams = {
      ...params,
    };
    fetchModalBookmarkList(searchParams);
  };
  const onOk = () => {
    onSelectModalOk(selectedItem);
  };
  const searchItem = [
    {
      label: '书签编码',
      id: 'code',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    },
    {
      label: '书签名称',
      id: 'name',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
  ];
  const tableColumns = [
    {
      title: '书签编码',
      dataIndex: 'code',
    },
    {
      title: '书签名称',
      dataIndex: 'name',
    },
    {
      title: '书签类型',
      dataIndex: 'type',
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      changeSelectedItem(selectedRows);
    },
  };
  return (
    <Modal
      title="选择书签"
      width={800}
      isAdaptable={false}
      visible={visible}
      onOk={() => onOk()}
      onCancel={onCancel}
    >
      <Spin spinning={modalBookmarkListLoading}>
        <ZcyList
          column={3}
          initSearchParams={{
            pageNo: 1,
            pageSize: 20,
          }}
          onSearch={onSearch}
          customItem={searchItem}
          table={{
            rowSelection: { type: 'radio', ...rowSelection },
            columns: tableColumns,
            dataSource: modalBookmarkList,
            rowKey: 'id',
            pagination: modalBookmarkListPagination,
          }}
        />
      </Spin>
    </Modal>
  );
};
export default SelectBookMarkModal;
