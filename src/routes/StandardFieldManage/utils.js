export const transformedData = (data, discList) => {
  const {
    bizType: bizTypeList = [],
    componentType: componentTypeList = [],
    stdlibTag: stdlibTagList = [],
  } = discList;
  const multiTypeList = [
    {
      value: 1,
      label: '单选',
    },
    {
      value: 2,
      label: '多选',
    },
    {
      value: 3,
      label: 'tag',
    },
  ];
  return {
    ...data,
    bizType: convertValueToLabel(data.bizType, bizTypeList),
    componentType: convertValueToLabel(data.componentType, componentTypeList),
    multiType: convertValueToLabel(data.multiType, multiTypeList),
    ifRow: convertRadioValueToLabel(data.ifRow),
    ifMust: convertRadioValueToLabel(data.ifMust),
    tags: convertArrayToLabel(data.tags, stdlibTagList),

  };
};

export const convertValueToLabel = (value, list) => {
  return list.find(item => item.value === value)?.label;
};

export const convertRadioValueToLabel = (value) => {
  if (value === undefined) {
    return undefined;
  }
  return value ? '是' : '否';
};

export const convertArrayToLabel = (value, list) => {
  return value?.map((i) => {
    const foundItem = list.find(item => item.value === i);
    return foundItem ? foundItem.label : value;
  }).filter(Boolean).join(',');
};

// 获取路径最后一个参数
export const getLastPathParam = () => {
  const fullPath = window.location.hash 
    ? window.location.hash.substring(1) 
    : window.location.pathname;

  // 使用正则匹配最后一个 `/` 后的内容
  const match = fullPath.match(/\/([^/]+)\/?$/);
  return match ? match[1] : null;
};
