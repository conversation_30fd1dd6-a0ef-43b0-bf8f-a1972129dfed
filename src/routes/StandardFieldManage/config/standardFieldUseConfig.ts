// 标准字段使用表单字段配置
import { PAGE_MODE } from '../constants';

export const standardFieldUseConfig = ({ mode, bizType, allAnnTypeList }) => {
  return [
    // ========== 新增模式字段 ==========
    {
      name: 'stdlibNames',
      label: '标准字段名称',
      type: 'select',
      placeholder: '请选择标准字段名称',
      visible: mode === PAGE_MODE.CREATE,
      required: mode === PAGE_MODE.CREATE,
      isMultiple: true, // 多选
      rules: [
        {
          required: true,
          message: '请选择标准字段名称',
        },
      ],
      options: [], // 将在组件中动态设置
    },

    // ========== 编辑模式字段（只读） ==========
    {
      name: 'stdlibCode',
      label: '标准字段code',
      type: 'input',
      placeholder: '',
      visible: mode === PAGE_MODE.EDIT,
      required: false,
      disabled: true, // 只读
    },
    {
      name: 'stdlibName',
      label: '标准字段名称',
      type: 'input',
      placeholder: '',
      visible: mode === PAGE_MODE.EDIT,
      required: false,
      disabled: true, // 只读
    },
    {
      name: 'stdlibTag',
      label: '书签类型',
      type: 'input',
      placeholder: '',
      visible: mode === PAGE_MODE.EDIT,
      required: false,
      disabled: true, // 只读
    },

    // ========== 通用字段（新增和编辑都有） ==========
    {
      name: 'bizType',
      label: '业务类型',
      type: 'select',
      placeholder: '请选择业务类型',
      visible: true,
      required: true,
      rules: [
        {
          required: true,
          message: '请选择业务类型',
        },
      ],
      options: bizType, // 将在组件中动态设置
    },
    {
      name: 'annType',
      label: '公告类型',
      type: 'treeSelect',
      placeholder: '请选择公告类型（只能选择具体类型）',
      visible: true,
      required: true,
      rules: [
        {
          required: true,
          message: '请选择公告类型',
        },
      ],
      treeData: allAnnTypeList, // 将在组件中动态设置
      showSearch: true,
      treeNodeFilterProp: 'title',
    },
    {
      name: 'districtCode',
      label: '区划',
      type: 'treeSelect',
      placeholder: '请选择区划',
      visible: true, // 注意：题目说只有区划级标准字段引用有，可能需要根据条件显示
      required: false,
      treeData: [], // 将在组件中动态设置
      showSearch: true,
      treeNodeFilterProp: 'title',
    },
    {
      name: 'forAnnPush',
      label: '是否用于公告推送',
      type: 'radio',
      placeholder: '',
      visible: true,
      required: true,
      rules: [
        {
          required: true,
          message: '请选择是否用于公告推送',
        },
      ],
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    {
      name: 'forFormRender',
      label: '是否用于表单渲染',
      type: 'radio',
      placeholder: '',
      visible: true,
      required: true,
      rules: [
        {
          required: true,
          message: '请选择是否用于表单渲染',
        },
      ],
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    {
      name: 'alias',
      label: '别名',
      type: 'input',
      placeholder: '请输入别名',
      visible: mode === PAGE_MODE.EDIT, // 只在编辑时显示
      required: false,
    },
  ];
};
