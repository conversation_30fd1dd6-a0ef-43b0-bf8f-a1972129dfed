/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-22 20:19:50
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 15:43:38
 */
// 表单字段配置，根据mode控制显隐
import { PAGE_MODE } from '../constants';

// visible 是否有改字段
// disabled 是否禁用

export const standardFieldsConfig = (mode) => [
  {
    name: 'name',
    label: '字段名称',
    type: 'input',
    placeholder: '请输入字段名称',
    visible: true,
    disabled: mode !== PAGE_MODE.CREATE,
    rules: [
      {
        required: true,
        message: '请输入字段编码',
      },
    ],
  },
  {
    name: 'code',
    label: '字段编码',
    type: 'input',
    placeholder: '请输入字段编码',
    visible: true,
    disabled: mode !== PAGE_MODE.CREATE,
    rules: [
      {
        required: true,
        message: '请输入字段编码',
      },
    ],
  },
  {
    name: 'bizType',
    label: '业务类型',
    type: 'select',
    placeholder: '请选择控件类型',
    visible: true,
    disabled: mode !== PAGE_MODE.CREATE,
    options: [
      {
        label: '政采',
        value: 1,
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择业务类型',
      },
    ],
    isNeedTransferData: true,
  },
  {
    name: 'componentType',
    label: '控件类型',
    type: 'select',
    placeholder: '请选择控件类型',
    visible: true,
    options: [],
    rules: [
      {
        required: true,
        message: '请选择控件类型',
      },
    ],
  },
  {
    name: 'minCNLength',
    label: '最小长度限制',
    type: 'inputNumber',
    placeholder: '请输入最小长度限制',
    visible: true,
    relateTo: {
      name: 'componentType',
      value: 6,
    },  
    extraParams: {
      min: 0,
      max: 100,
      precision: 0,
    },
    extra: '汉字必须大于此个数',
  },
  {
    name: 'multiType',
    label: '多选类型',
    type: 'radio',
    placeholder: '请选择是否一行',
    visible: true,
    options: [
      {
        label: '单选',
        value: 1,
      },
      {
        label: '多选',
        value: 2,
      },
      {
        label: 'tag',
        value: 3,
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择多选类型',
      },
    ],
    relateTo: {
      name: 'componentType',
      value: 1,
    },  
  },
  {
    name: 'ifRow',
    label: '是否一行',
    type: 'radio',
    visible: true,
    rules: [
      {
        required: true,
        message: '请选择是否一行',
      },
    ],
    options: [
      {
        label: '是',
        value: 1,
      },
      {
        label: '否',
        value: 0,
      },
    ],
  },
  {
    name: 'ifMust',
    label: '是否必填',
    type: 'radio',
    visible: true,
    options: [
      {
        label: '是',
        value: 1,
      },
      {
        label: '否',
        value: 0,
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择是否必填',
      },
    ],
  },
  {
    name: 'tags',
    label: '用途',
    type: 'select',
    placeholder: '请选择控件类型',
    visible: true,
    isMultiple: true,
    options: [],
  },
  {
    name: 'remark',
    label: '说明',
    type: 'input',
    placeholder: '请输入说明',
    visible: true,
  },
  {
    name: 'example',
    label: '示例',
    type: 'textArea',
    placeholder: '请输入示例',
    visible: true,
  },
  {
    name: 'children',
    label: '子标准字段',
    type: 'table',
    colSpan: 2,
    placeholder: '请选择标准字段名称',
    visible: mode === PAGE_MODE.DETAIL,
    disabled: true,
    columns: [
      {
        title: '字段名称',
        dataIndex: 'name',
        key: 'name',
        width: 100,
      },
      {
        title: '字段编码',
        dataIndex: 'code',
        key: 'code',
        width: 100,
      },
      {
        title: '控件类型',
        dataIndex: 'componentType',
        key: 'componentType',
        width: 100,
      },
    ],
    ifPagination: false,
  },
  {
    name: 'templateUse',
    label: '模板引用情况',
    type: 'table',
    colSpan: 2,
    visible: mode === PAGE_MODE.TEMPLATEUSE,
    columns: [
      {
        title: '影响的书签',
        dataIndex: 'bookmark',
        key: 'bookmark',
        width: 120,
      },
      {
        title: '影响的模板名称',
        dataIndex: 'templateName',
        key: 'templateName',
        width: 150,
      },
      {
        title: '影响的模板编码',
        dataIndex: 'templateCode',
        key: 'templateCode',
        width: 150,
      },
    ],
  },
  {
    name: 'logsInfo',
    label: '日志信息',
    type: 'table',
    colSpan: 2,
    visible: mode === PAGE_MODE.DETAIL,
    columns: [
      {
        title: '操作人',
        dataIndex: 'operatorName',
        key: 'bookmark',
        width: 100,
      },
      {
        title: '修改时间',
        dataIndex: 'modifiedTime',
        key: 'templateName',
        width: 150,
      },
      {
        title: '操作类型',
        dataIndex: 'operationTypeName',
        key: 'operationTypeName',
        width: 100,
      },
      {
        title: '操作内容',
        dataIndex: 'message',
        key: 'message',
        width: 150,
      },
    ],
    ifPagination: true,
  },
];
