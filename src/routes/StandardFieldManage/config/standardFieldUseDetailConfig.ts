/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-06-06 23:15:00
 * @Description: 标准字段使用详情字段配置
 */
import { PAGE_MODE } from '../constants';

// 详情表单字段配置
export const standardFieldUseDetailConfig = (mode: string) => [
  {
    name: 'stdlibCode',
    label: '标准字段code',
    type: 'input',
    visible: true,
    disabled: true,
  },
  {
    name: 'stdlibName',
    label: '标准字段名称',
    type: 'input',
    visible: true,
    disabled: true,
  },
  {
    name: 'stdlibType',
    label: '控件类型',
    type: 'input',
    visible: true,
    disabled: true,
  },
  {
    name: 'forFormRender',
    label: '是否用于表单渲染',
    type: 'radio',
    visible: true,
    disabled: true,
    options: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
  },
  {
    name: 'forAnnPush',
    label: '是否用于公告推送',
    type: 'radio',
    visible: true,
    disabled: true,
    options: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
  },
  {
    name: 'referStatus',
    label: '引用状态',
    type: 'radio',
    visible: true,
    disabled: true,
    options: [
      {
        label: '已启用',
        value: 1,
      },
      {
        label: '已停用',
        value: 0,
      },
    ],
  },
  {
    name: 'modifyName',
    label: '更新人',
    type: 'input',
    visible: true,
    disabled: true,
  },
  {
    name: 'modifyTime',
    label: '更新时间',
    type: 'input',
    visible: true,
    disabled: true,
  },
  {
    name: 'children',
    label: '子引用字段',
    type: 'table',
    colSpan: 2,
    visible: true,
    disabled: true,
    columns: [
      {
        title: '标准字段code',
        dataIndex: 'stdlibCode',
        key: 'stdlibCode',
        width: 120,
      },
      {
        title: '标准字段名称',
        dataIndex: 'stdlibName',
        key: 'stdlibName',
        width: 120,
      },
      {
        title: '控件类型',
        dataIndex: 'stdlibType',
        key: 'stdlibType',
        width: 120,
      },
      {
        title: '引用状态',
        dataIndex: 'referStatus',
        key: 'referStatus',
        width: 100,
      },
    ],
    ifPagination: false,
  },
];
