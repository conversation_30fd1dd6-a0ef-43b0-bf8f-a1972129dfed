/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-22 20:19:50
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-06 15:41:06
 */
// 表单字段配置，根据mode控制显隐
import { PAGE_MODE } from '../constants';

export const bookMarkFieldsConfig = (mode) => [
  {
    name: 'code',
    label: '书签编码',
    type: 'input',
    placeholder: '请输入书签编码',
    visible: true,
    disabled: true,
    rules: [
      {
        required: true,
        message: '请输入书签编码',
      },
    ],
    isText: mode === 'templateUse',
  },
  {
    name: 'name',
    label: '书签名称',
    type: 'input',
    placeholder: '请输入书签名称',
    visible: true,
    disabled: true,
    rules: [
      {
        required: true,
        message: '请输入书签编码',
      },
    ],
    isText: mode === 'templateUse',
  },
  {
    name: 'type',
    label: '书签类型',
    type: 'input',
    placeholder: '请输入书签编码',
    visible: true,
    disabled: true,
    rules: [
      {
        required: true,
        message: '请选择书签类型',
      },
    ],
    isText: mode === 'templateUse',
  },
  {
    name: 'stdlibName',
    label: '标准字段名称',
    type: 'select',
    placeholder: '请选择标准字段名称',
    visible: true,
    rules: [
      {
        required: true,
        message: '请选择标准字段名称',
      },
    ],
    isNeedSearch: true,
    isText: mode === 'templateUse',
  },
  {
    name: 'stdlibCode',
    label: '标准字段编号',
    type: 'input',
    placeholder: '',
    visible: true,
    disabled: true,
    isText: mode === 'templateUse',
  },
  {
    name: 'stdlibId',
    label: '标准字段id',
    type: 'input',
    visible: true,
    disabled: true,
    isText: mode === 'templateUse',
    isHidden: true,
  },
  {
    name: 'bizType',
    label: '业务类型',
    type: 'select',
    placeholder: '请选择控件类型',
    visible: true,
    options: [
      {
        label: '政采',
        value: 1,
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择业务类型',
      },
    ],
    isNeedTransferData: true,
  },
  {
    name: 'templateUse',
    label: '模板引用情况',
    type: 'table',
    colSpan: 2,
    visible: mode === PAGE_MODE.TEMPLATEUSE,
    columns: [
      {
        title: '影响的书签',
        dataIndex: 'bookmark',
        key: 'bookmark',
        width: 100,
      },
      {
        title: '影响的模板名称',
        dataIndex: 'templateName',
        key: 'templateName',
        width: 150,
      },
      {
        title: '影响的模板编码',
        dataIndex: 'templateCode',
        key: 'templateCode',
        width: 150,
      },
    ],
  },
  {
    name: 'logsInfo',
    label: '日志信息',
    type: 'table',
    visible: mode === PAGE_MODE.DETAIL,
    colSpan: 2,
    columns: [
      {
        title: '操作人',
        dataIndex: 'operatorName',
        key: 'bookmark',
        width: 100,
      },
      {
        title: '修改时间',
        dataIndex: 'modifiedTime',
        key: 'templateName',
        width: 150,
      },
      {
        title: '操作类型',
        dataIndex: 'operationTypeName',
        key: 'operationTypeName',
        width: 100,
      },
      {
        title: '操作内容',
        dataIndex: 'message',
        key: 'message',
        width: 150,
      },
    ],
    ifPagination: true,
  },
];
