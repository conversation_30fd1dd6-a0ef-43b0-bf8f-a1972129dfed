/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 16:41:56
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-05 15:38:45
 * @FilePath: /zcy-announcement-v2-front/src/routes/StandardFieldManage/services/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@zcy/zcy-request';
import Cookies from 'js-cookie';

const commonRequest = (url, options = {}) => {
  const env = getCurrentEnv(); // 获取当前环境
  
  return request(url, {
    ...options,
    headers: {
      env,
    },
  });
};

const getCurrentEnv = () => {
  const env = Cookies.get('envName');
  return env;
};

// 获取静态字典列表
export async function fetchDictList(params) {
  if (params.codes) {
    params.codes = params.codes.join(',');
  }
  return commonRequest('/api/opPlatform/prodOperation/announcement/dict', {
    method: 'GET',
    params,
  });
}

export async function fetchButtonList(params) {
  return commonRequest('/api/opPlatform/prodOperation/announcement/config', {
    method: 'GET',
    params,
  });
}

// 获取标准字段列表
export async function fetchStandardFieldList(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/list', {
      method: 'GET',
      params,
    });
}

// 保存标准字段
export async function updateStandardField(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/save', {
      method: 'POST',
      data: params,
    }
  );
}

// 显示使用书签和模板（标准字段）
export async function fetchTemplateUseArray(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/showUsedInfo', {
      method: 'GET',
      params,
    }
  );
}

// 标准字段详情
export async function fetchStandardFieldsDetail(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/detail', {
      method: 'GET',
      params,
    }
  );
}

// 启用标准字段
export async function fetchEnableStandardFields(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/enable', {
      method: 'POST',
      data: params,
    }
  );
}
// 停用标准字段
export async function fetchDisableStandardFields(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/disable', {
      method: 'POST',
      data: params,
    }
  );
}

// 删除标准字段
export async function fetchDeleteStandardFields(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/stdlib/delete', {
      method: 'POST',
      data: params,
    }
  );
}

// 获取书签列表
export async function fetchBookmarkList(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/list', {
      method: 'GET',
      params,
    }
  );
}

// 查询书签
export async function fetchModalBookmarkListApi(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark', {
      method: 'GET',
      params,
    }
  );
}

// 保存书签
export async function updateTemplateBookmarkApi(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/save', {
      method: 'POST',
      data: params,
    }
  );
}

// 显示使用书签和模板（书签页）
export async function fetchTemplateUseList(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo', {
      method: 'GET',
      params,
    }
  );
}

// 书签详情
export async function fetchBookmarkDetail(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/detail', {
      method: 'GET',
      params,
    }
  );
}

// 启用书签
export async function fetchEnableBookmark(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/enable', {
      method: 'POST',
      data: params,
    }
  );
}

// 停用书签
export async function fetchDisableBookmark(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/disable', {
      method: 'POST',
      data: params,
    }
  );
}
// 删除书签
export async function fetchDeleteBookmark(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/bookmark/delete', {
      method: 'POST',
      data: params,
    }
  );
}

// 日志获取信息
export async function fetchLogsInfo(params) {
  return commonRequest(
    '/api/opPlatform/prodOperation/announcement/logs', {
      method: 'GET',
      params,
    }
  );
}
// 标题库字段引用页面

// 获取当前环境所有公告类型
export async function fetchReferConfigAllAnnType(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/referConfig/allAnnType', {
      method: 'GET',
      params,
    }
  );
}
// 获取标准字段使用列表
export async function fetchStandardFieldUseList(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/standardField/useList', {
      method: 'GET',
      params,
    }
  );
}

// 保存标准字段使用配置
export async function saveStandardFieldUse(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/standardField/save', {
      method: 'POST',
      data: params,
    }
  );
}

// 删除标准字段使用配置
export async function deleteStandardFieldUse(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/standardField/delete', {
      method: 'DELETE',
      params,
    }
  );
}

// 获取标准字段使用详情
export async function fetchStandardFieldUseDetail(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/standardField/detail', {
      method: 'GET',
      params,
    }
  );
}

// 从其他公告类型复制标准字段配置
export async function copyStandardFieldFromOtherType(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/standardField/copy', {
      method: 'POST',
      data: params,
    }
  );
}

// 获取标准字段使用日志
export async function fetchStandardFieldUseLog(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/standardField/log', {
      method: 'GET',
      params,
    }
  );
}
