/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_2024_57897 = '' as any
const devUrl_2024_57897 = '' as any
const prodUrl_2024_57897 = '' as any
const dataKey_2024_57897 = 'data' as any

/**
 * 接口 [列表查询↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/list`
 * @项目ID 2024
 */
export interface ListGetRequest {
  /**
   * 书签Code
   */
  code?: string
  /**
   * 书签名称
   */
  name?: string
  /**
   * 标准字段Code
   */
  stdlibCode?: string
  /**
   * 标准字段名称
   */
  stdlibName?: string
  /**
   * 业务类型
   */
  bizType?: string
  pageNo?: string
  pageSize?: string
}

/**
 * 接口 [列表查询↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/list`
 * @项目ID 2024
 */
export interface ListGetResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: {
    /**
     * 当前页码
     */
    pageNo?: number
    /**
     * 每页展示条数
     */
    pageSize?: number
    /**
     * 总页数
     */
    pages?: number
    /**
     * 总条数
     */
    total?: number
    data?: {
      /**
       * 按钮
       */
      buttonList?: {
        code?: string
        name?: string
      }[]
      /**
       * 主键
       */
      id?: number
      /**
       * 书签Code
       */
      code?: string
      /**
       * 书签名称
       */
      name?: string
      /**
       * 书签类型
       */
      type?: string
      /**
       * 标准库id
       */
      stdlibId?: number
      /**
       * 标准库Code
       */
      stdlibCode?: string
      /**
       * 标准库名称
       */
      stdlibName?: string
      /**
       * 状态
       */
      status?: string
      /**
       * 创建人
       */
      createName?: string
      /**
       * 创建时间
       */
      createTime?: string
      /**
       * 修改人
       */
      modifyName?: string
      /**
       * 修改时间
       */
      modifyTime?: string
    }[]
  }
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [列表查询↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/list`
 * @项目ID 2024
 */
type ListGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/list',
    'data',
    string,
    'code' | 'name' | 'stdlibCode' | 'stdlibName' | 'bizType' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [列表查询↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/list`
 * @项目ID 2024
 */
const listGetRequestConfig: ListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/list',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['code', 'name', 'stdlibCode', 'stdlibName', 'bizType', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [列表查询↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/list`
 * @项目ID 2024
 */
export const listGet = /*#__PURE__*/ (requestData: ListGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListGetResponse>(prepare(listGetRequestConfig, requestData), ...args)
}

listGet.requestConfig = listGetRequestConfig

/**
 * 接口 [详情↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/detail`
 * @项目ID 2024
 */
export interface DetailGetRequest {
  id: string
}

/**
 * 接口 [详情↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/detail`
 * @项目ID 2024
 */
export interface DetailGetResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: {
    /**
     * 主键
     */
    id?: number
    /**
     * 书签Code
     */
    code?: string
    /**
     * 书签名称
     */
    name?: string
    /**
     * 书签类型
     */
    type?: string
    /**
     * 标准库id
     */
    stdlibId?: number
    /**
     * 标准库Code
     */
    stdlibCode?: string
    /**
     * 标准库名称
     */
    stdlibName?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 创建人
     */
    createName?: string
    /**
     * 创建时间
     */
    createTime?: string
    /**
     * 修改人
     */
    modifyName?: string
    /**
     * 修改时间
     */
    modifyTime?: string
  }
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [详情↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/detail`
 * @项目ID 2024
 */
type DetailGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/detail',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [详情↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/detail`
 * @项目ID 2024
 */
const detailGetRequestConfig: DetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/detail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'detailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [详情↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/detail`
 * @项目ID 2024
 */
export const detailGet = /*#__PURE__*/ (requestData: DetailGetRequest, ...args: UserRequestRestArgs) => {
  return request<DetailGetResponse>(prepare(detailGetRequestConfig, requestData), ...args)
}

detailGet.requestConfig = detailGetRequestConfig

/**
 * 接口 [保存↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/save`
 * @项目ID 2024
 */
export interface SavePostRequest {
  /**
   * 主键
   */
  id?: number
  /**
   * 书签Code
   */
  code?: string
  /**
   * 书签名称
   */
  name?: string
  /**
   * 书签类型
   */
  type?: string
  /**
   * 标准库id
   */
  stdlibId?: number
  /**
   * 标准库Code
   */
  stdlibCode?: string
  /**
   * 标准库名称
   */
  stdlibName?: string
  /**
   * 状态
   */
  status?: string
}

/**
 * 接口 [保存↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/save`
 * @项目ID 2024
 */
export interface SavePostResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: boolean
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [保存↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/save`
 * @项目ID 2024
 */
type SavePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/save',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/save`
 * @项目ID 2024
 */
const savePostRequestConfig: SavePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/save',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'savePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/save`
 * @项目ID 2024
 */
export const savePost = /*#__PURE__*/ (requestData: SavePostRequest, ...args: UserRequestRestArgs) => {
  return request<SavePostResponse>(prepare(savePostRequestConfig, requestData), ...args)
}

savePost.requestConfig = savePostRequestConfig

/**
 * 接口 [启用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/enable`
 * @项目ID 2024
 */
export interface EnablePostRequest {
  id: string
}

/**
 * 接口 [启用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/enable`
 * @项目ID 2024
 */
export interface EnablePostResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: boolean
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [启用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/enable`
 * @项目ID 2024
 */
type EnablePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/enable',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [启用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/enable`
 * @项目ID 2024
 */
const enablePostRequestConfig: EnablePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/enable',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.none,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'enablePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [启用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/enable`
 * @项目ID 2024
 */
export const enablePost = /*#__PURE__*/ (requestData: EnablePostRequest, ...args: UserRequestRestArgs) => {
  return request<EnablePostResponse>(prepare(enablePostRequestConfig, requestData), ...args)
}

enablePost.requestConfig = enablePostRequestConfig

/**
 * 接口 [停用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/disable`
 * @项目ID 2024
 */
export interface DisablePostRequest {
  id: string
}

/**
 * 接口 [停用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/disable`
 * @项目ID 2024
 */
export interface DisablePostResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: boolean
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [停用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/disable`
 * @项目ID 2024
 */
type DisablePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/disable',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [停用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/disable`
 * @项目ID 2024
 */
const disablePostRequestConfig: DisablePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/disable',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.none,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'disablePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [停用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/disable`
 * @项目ID 2024
 */
export const disablePost = /*#__PURE__*/ (requestData: DisablePostRequest, ...args: UserRequestRestArgs) => {
  return request<DisablePostResponse>(prepare(disablePostRequestConfig, requestData), ...args)
}

disablePost.requestConfig = disablePostRequestConfig

/**
 * 接口 [同步到其他环境↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/sync`
 * @项目ID 2024
 */
export interface SyncPostRequest {
  ids?: string
  freeWay?: string
}

/**
 * 接口 [同步到其他环境↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/sync`
 * @项目ID 2024
 */
export interface SyncPostResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: boolean
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [同步到其他环境↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/sync`
 * @项目ID 2024
 */
type SyncPostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/sync',
    'data',
    string,
    'ids' | 'freeWay',
    false
  >
>

/**
 * 接口 [同步到其他环境↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/sync`
 * @项目ID 2024
 */
const syncPostRequestConfig: SyncPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/sync',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.none,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['ids', 'freeWay'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [同步到其他环境↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/sync`
 * @项目ID 2024
 */
export const syncPost = /*#__PURE__*/ (requestData: SyncPostRequest, ...args: UserRequestRestArgs) => {
  return request<SyncPostResponse>(prepare(syncPostRequestConfig, requestData), ...args)
}

syncPost.requestConfig = syncPostRequestConfig

/**
 * 接口 [显示同步差异↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showSyncDif`
 * @项目ID 2024
 */
export interface ShowSyncDifGetRequest {
  ids?: string
}

/**
 * 接口 [显示同步差异↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showSyncDif`
 * @项目ID 2024
 */
export interface ShowSyncDifGetResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: {
    /**
     * 编号
     */
    code?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 更新类型：新增、更新
     */
    updateType?: string
  }[]
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [显示同步差异↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showSyncDif`
 * @项目ID 2024
 */
type ShowSyncDifGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/showSyncDif',
    'data',
    string,
    'ids',
    false
  >
>

/**
 * 接口 [显示同步差异↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showSyncDif`
 * @项目ID 2024
 */
const showSyncDifGetRequestConfig: ShowSyncDifGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/showSyncDif',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['ids'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'showSyncDifGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [显示同步差异↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showSyncDif`
 * @项目ID 2024
 */
export const showSyncDifGet = /*#__PURE__*/ (requestData: ShowSyncDifGetRequest, ...args: UserRequestRestArgs) => {
  return request<ShowSyncDifGetResponse>(prepare(showSyncDifGetRequestConfig, requestData), ...args)
}

showSyncDifGet.requestConfig = showSyncDifGetRequestConfig

/**
 * 接口 [显示使用书签和模板↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo`
 * @项目ID 2024
 */
export interface ShowUsedInfoGetRequest {
  id: string
}

/**
 * 接口 [显示使用书签和模板↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo`
 * @项目ID 2024
 */
export interface ShowUsedInfoGetResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: {
    /**
     * 影响的书签
     */
    bookmark?: string
    /**
     * 影响的模板
     */
    templates?: {
      /**
       * 模板文件id
       */
      templateId?: number
      /**
       * 模板编码
       */
      templateCode?: string
      /**
       * 默认模板文件id
       */
      defaultTemplateFileId?: number
      /**
       * 模板名称
       */
      templateName?: string
      /**
       * 模板级别
       */
      templateLevelName?: string
      /**
       * 模板版本
       */
      templateFileVersion?: string
    }[]
  }[]
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [显示使用书签和模板↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo`
 * @项目ID 2024
 */
type ShowUsedInfoGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [显示使用书签和模板↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo`
 * @项目ID 2024
 */
const showUsedInfoGetRequestConfig: ShowUsedInfoGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'showUsedInfoGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [显示使用书签和模板↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/showUsedInfo`
 * @项目ID 2024
 */
export const showUsedInfoGet = /*#__PURE__*/ (requestData: ShowUsedInfoGetRequest, ...args: UserRequestRestArgs) => {
  return request<ShowUsedInfoGetResponse>(prepare(showUsedInfoGetRequestConfig, requestData), ...args)
}

showUsedInfoGet.requestConfig = showUsedInfoGetRequestConfig

/**
 * 接口 [查询模板书签↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/queryBookmark`
 * @项目ID 2024
 */
export interface QueryBookmarkGetRequest {
  /**
   * 书签Code
   */
  code?: string
  /**
   * 书签名称
   */
  name?: string
  pageNo?: string
  pageSize?: string
}

/**
 * 接口 [查询模板书签↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/queryBookmark`
 * @项目ID 2024
 */
export interface QueryBookmarkGetResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: {
    /**
     * 当前页码
     */
    pageNo?: number
    /**
     * 每页展示条数
     */
    pageSize?: number
    /**
     * 总页数
     */
    pages?: number
    /**
     * 总条数
     */
    total?: number
    data?: {
      /**
       * 书签Code
       */
      code?: string
      /**
       * 书签名称
       */
      name?: string
      /**
       * 书签类型
       */
      type?: string
    }[]
  }
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [查询模板书签↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/queryBookmark`
 * @项目ID 2024
 */
type QueryBookmarkGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark',
    'data',
    string,
    'code' | 'name' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [查询模板书签↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/queryBookmark`
 * @项目ID 2024
 */
const queryBookmarkGetRequestConfig: QueryBookmarkGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['code', 'name', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'queryBookmarkGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询模板书签↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/bookmark/queryBookmark`
 * @项目ID 2024
 */
export const queryBookmarkGet = /*#__PURE__*/ (requestData: QueryBookmarkGetRequest, ...args: UserRequestRestArgs) => {
  return request<QueryBookmarkGetResponse>(prepare(queryBookmarkGetRequestConfig, requestData), ...args)
}

queryBookmarkGet.requestConfig = queryBookmarkGetRequestConfig

/**
 * 接口 [删除↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/delete`
 * @项目ID 2024
 */
export interface DeletePostRequest {
  id: string
}

/**
 * 接口 [删除↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/delete`
 * @项目ID 2024
 */
export interface DeletePostResponse {
  /**
   * 必填 是否成功标识
   */
  success?: boolean
  /**
   * 非必填 返回结果集
   */
  result?: boolean
  /**
   * 失败时必填 业务码/失败码 全局需唯一
   */
  code?: string
  /**
   * 失败时必填 业务文案/失败文案提示
   */
  message?: string
}

/**
 * 接口 [删除↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/delete`
 * @项目ID 2024
 */
type DeletePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/bookmark/delete',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [删除↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/delete`
 * @项目ID 2024
 */
const deletePostRequestConfig: DeletePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_57897,
  devUrl: devUrl_2024_57897,
  prodUrl: prodUrl_2024_57897,
  path: '/api/opPlatform/prodOperation/announcement/bookmark/delete',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.none,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_57897,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'deletePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementBookmarkController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/bookmark/delete`
 * @项目ID 2024
 */
export const deletePost = /*#__PURE__*/ (requestData: DeletePostRequest, ...args: UserRequestRestArgs) => {
  return request<DeletePostResponse>(prepare(deletePostRequestConfig, requestData), ...args)
}

deletePost.requestConfig = deletePostRequestConfig

/* prettier-ignore-end */
