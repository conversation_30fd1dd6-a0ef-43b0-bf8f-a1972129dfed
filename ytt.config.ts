/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-04-26 15:37:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-03-21 15:27:55
 * @FilePath: /zcy-announcement-v2-front/ytt.config.ts
 * @Description: 
 */
import { defineConfig } from 'yapi-to-typescript';
/**
 * 注：每新增一个文件夹的接口就需要增加映射
 * project_id 和 生成的目标文件名映射
 * 如：http://yapi.cai-inc.com/project/3705/interface/api
 * project_id为 3705
 */
const projectNameMap = {
  // [project_id]: '生成目标文件的名称'
  2024: '',
};

export default defineConfig([
  {
    serverUrl: 'http://yapi.cai-inc.com/',
    serverType: 'yapi',
    typesOnly: false,
    target: 'typescript',
    reactHooks: {
      enabled: false,
    },
    comment: {
      enabled: true,
      title: true,
      category: true,
      tag: true,
      requestHeader: true,
      updateTime: false,
      link: true,
      extraTags: ii => [
        {
          name: '状态',
          value: ii.status === 'done' ? '已完成' : '未完成',
          position: 'start',
        },
        {
          name: '项目ID',
          value: ii.project_id.toString(),
        },
      ],
    },
    outputFilePath: (interfaceInfo) => {
      const pathList = interfaceInfo.path.split('/');
      pathList.pop();
      return `src/api/announcement/opPlatform/prodOperation/announcement/announcementReferConfigController.ts`;
    },
    requestFunctionFilePath: 'src/ajax/opPlatform.ts',
    dataKey: 'data',
    projects: [
      {
        /**
         * 关联一个项目
         */
        // token: string | string[];
        token: 'aef28517d2ee646b03b263d1a3587fcac362f9aa6ed4af8d665fe8d47b8753d4',
        categories: [
          {
            /**
             * id: number | number[];
             * 设为 `0` 时表示全部分类。如果需要获取全部分类，同时排除指定分类，可以这样：`[0, -20, -21]`，分类 ID 前面的负号表示排除。
             */
            id: [
              // 51179, // AnnouncementTypeGroupController 分组
              // 51046, // AnnouncementTypeController 公告类型配置   
              // 51175, // AnnouncementTemplateController
              // 51048, // DistrictTreeController 区划树相关   
              // 51045, // AnnouncementSiteConfigController 公告站点配置   
              // 51790, // AnnouncementGuideController 公告接入管理  
              // 51061,
              // 51066,
              // 51045,
              // 52288,
              // 51067, // AnnPushStatisticsController
              // 51043, // AnnouncementRelationController 公告类型/关联配置 
              // 51044, // AnnouncementPublicController 公告发布配置管理   
              // 51065, // AnnouncementObjectionController 公告异议   
              // 51062, // AnnouncementImportExportController 公告导入导出controller   
              // 52288, // AnnouncementAdminController
              // 51052, // AnnouncementNotifyController 公告发布短信推送相关接口
              // 51053, // AnnouncementOpenController 公告对外rest接口   
              // 53099, // TestController
              // 52288, // AnnouncementAdminController
              // 53626,
              // 51050,
              // 51066,
              // 51045,
              51055,
            ], 
            getRequestFunctionName(interfaceInfo, changeCase) {
              return changeCase.camelCase(`${interfaceInfo.parsedPath.name}_${interfaceInfo.method}`);
            },
          },
        ],
        getRequestFunctionName(interfaceInfo, changeCase) {
          return changeCase.camelCase(`${interfaceInfo.parsedPath.name}_${interfaceInfo.method}`);
        },
      }],
  },
]);
